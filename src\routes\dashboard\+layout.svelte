<script lang="ts">
  import { goto } from '$app/navigation';
  import type { LayoutData } from './$types';

  export let data: LayoutData;
  let sidebarOpen = false;

  async function handleLogout() {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      goto('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  }
</script>

<svelte:head>
  <title>Dashboard - Routine Mail</title>
</svelte:head>

<div class="layout">
  <!-- Sidebar -->
  <aside class="sidebar" class:is-open={sidebarOpen}>
    <div class="sidebar-header">
      <a href="/dashboard" class="logo">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4 7.00005L10.2 11.65C11.2667 12.45 12.7333 12.45 13.8 11.65L20 7" stroke="#1F2937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><rect x="3" y="5" width="18" height="14" rx="2" stroke="#1F2937" stroke-width="2" stroke-linecap="round"></rect></svg>
        <span>Routine Mail</span>
      </a>
    </div>
    <nav class="nav-links">
      <a href="/dashboard" class="nav-link" on:click={() => sidebarOpen = false}>
        <svg class="nav-icon" viewBox="0 0 24 24"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
        <span>Dashboard</span>
      </a>
      <a href="/dashboard/tasks" class="nav-link" on:click={() => sidebarOpen = false}>
        <svg class="nav-icon" viewBox="0 0 24 24"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
        <span>Tasks</span>
      </a>
      <a href="/dashboard/settings" class="nav-link" on:click={() => sidebarOpen = false}>
       <svg class="nav-icon" viewBox="0 0 24 24"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 0 2l-.15.08a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l-.22-.38a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1 0-2l.15.08a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>
        <span>Settings</span>
      </a>
    </nav>
    <div class="sidebar-footer">
       <div class="user-profile">
        <span class="user-email">{data.user.email}</span>
        <button class="logout-btn" on:click={handleLogout}>Logout</button>
      </div>
    </div>
  </aside>
  
  {#if sidebarOpen}
    <div class="sidebar-overlay" on:click={() => sidebarOpen = false} />
  {/if}

  <!-- Main Content -->
  <div class="main-container">
    <header class="mobile-header">
       <a href="/dashboard" class="logo">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4 7.00005L10.2 11.65C11.2667 12.45 12.7333 12.45 13.8 11.65L20 7" stroke="#1F2937" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path><rect x="3" y="5" width="18" height="14" rx="2" stroke="#1F2937" stroke-width="2" stroke-linecap="round"></rect></svg>
      </a>
      <button class="hamburger-btn" on:click={() => sidebarOpen = !sidebarOpen}>
        <svg width="24" height="24" viewBox="0 0 24 24"><line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line></svg>
      </button>
    </header>
    <main class="main-content">
      <slot />
    </main>
  </div>
</div>


<style>
  :global(body) {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    background-color: #f8f9fa; /* Office-like light gray */
    color: #212529;
    margin: 0;
  }

  .layout {
    display: flex;
    min-height: 100vh;
  }

  /* Sidebar */
  .sidebar {
    width: 240px;
    background-color: #ffffff;
    border-right: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 1000;
  }

  .sidebar-header {
    padding: 1.5rem 1.25rem;
    border-bottom: 1px solid #dee2e6;
  }
  
  .logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #212529;
    text-decoration: none;
  }

  .nav-links {
    padding: 1rem 0;
    flex-grow: 1;
  }

  .nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.25rem;
    margin: 0 0.75rem 0.25rem;
    color: #495057;
    text-decoration: none;
    font-weight: 500;
    border-radius: 6px;
    transition: background-color 0.2s ease, color 0.2s ease;
  }

  .nav-link:hover {
    background-color: #f1f3f5;
    color: #007bff;
  }
  
  /* Active link styling could be added here later using page store */
  
  .nav-icon {
    width: 20px;
    height: 20px;
    stroke: currentColor;
    fill: none;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
  }

  .sidebar-footer {
    padding: 1.25rem;
    border-top: 1px solid #dee2e6;
  }
  
  .user-profile {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .user-email {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .logout-btn {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 6px;
    background-color: #f8f9fa;
    color: #495057;
    cursor: pointer;
    font-weight: 500;
    text-align: center;
    transition: background-color 0.2s ease, border-color 0.2s ease;
  }

  .logout-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
  }

  /* Main container */
  .main-container {
    flex-grow: 1;
    margin-left: 240px; /* Same as sidebar width */
    display: flex;
    flex-direction: column;
  }
  
  .main-content {
    padding: 2.5rem;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }

  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,0.5);
    z-index: 999;
    display: none;
  }

  .mobile-header {
    display: none;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background-color: #ffffff;
    border-bottom: 1px solid #dee2e6;
  }

  .hamburger-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
  }
  .hamburger-btn svg {
    stroke: #212529;
    stroke-width: 2;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .sidebar {
      transform: translateX(-100%);
      transition: transform 0.3s ease-in-out;
    }
    
    .sidebar.is-open {
      transform: translateX(0);
      box-shadow: 0 0 20px rgba(0,0,0,0.2);
    }

    .sidebar-overlay {
      display: block;
    }

    .main-container {
      margin-left: 0;
    }

    .main-content {
      padding: 1.5rem;
    }
    
    .mobile-header {
      display: flex;
    }
  }
</style>
