import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { verifyJWT } from '$lib/server/auth.js';
import { createCategory, getCategoriesByUserId } from '$lib/server/db/operations.js';

export const GET: RequestHandler = async ({ cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const categories = await getCategoriesByUserId(payload.userId);

    return json({ categories });
  } catch (error) {
    console.error('Get categories error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const { name, color } = await request.json();

    // Validate required fields
    if (!name || name.trim() === '') {
      return json({ error: 'Category name is required' }, { status: 400 });
    }

    // Validate color format (hex color)
    if (color && !/^#[0-9A-F]{6}$/i.test(color)) {
      return json({ error: 'Color must be a valid hex color (e.g., #FF0000)' }, { status: 400 });
    }

    const category = await createCategory({
      userId: payload.userId,
      name: name.trim(),
      color: color || '#6B7280'
    });

    return json({ category }, { status: 201 });
  } catch (error) {
    console.error('Create category error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
