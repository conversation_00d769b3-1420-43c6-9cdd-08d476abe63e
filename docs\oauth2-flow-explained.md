# OAuth2 Single Sign-On (SSO) 流程详解

## 📋 概述

这个文档详细解释了 Routine Mail 与 Office Flow 之间的 OAuth2 单点登录流程，包括每一步发生的事情、使用的 API、访问的页面，以及 JWT token 的处理。

## 🔄 完整流程图

```
用户 → Routine Mail → Office Flow → 用户授权 → 回调 → Routine Mail
```

## 📝 详细步骤分析

### 1. 用户点击 "Sign in with Office Flow"

**位置**: `http://localhost:3000/login`
**文件**: `src/routes/login/+page.svelte`

```javascript
// 用户点击按钮触发
async function handleOfficeFlowLogin() {
  await redirectToOfficeFlowLogin("/dashboard");
}
```

**发生的事情**:

- 调用 `redirectToOfficeFlowLogin()` 函数
- 这个函数在 `src/lib/client/auth.js` 中定义

### 2. 生成授权 URL

**API 调用**: `POST /api/auth/oauth2/authorize`
**文件**: `src/routes/api/auth/oauth2/authorize/+server.ts`

```javascript
// 生成授权URL
const { url } = generateAuthorizationUrl({
  redirectTo: "/dashboard",
  isLinking: false,
  usePopup: false,
});
```

**发生的事情**:

1. 生成随机的 `state` 参数（用于防止 CSRF 攻击）
2. 生成 `code_verifier` 和 `code_challenge`（PKCE 安全机制）
3. 将 state 数据存储到文件系统
4. 构建 Office Flow 的授权 URL

**生成的 URL 示例**:

```
http://localhost:8092/api/v1/oauth2/pre-authorize?
  client_id=routine-mail&
  redirect_uri=http://localhost:3000/auth/callback&
  response_type=code&
  scope=openid+profile+email+read&
  state=abc123...&
  code_challenge=xyz789...&
  code_challenge_method=S256
```

### 3. 重定向到 Office Flow 预认证端点

**URL**: `http://localhost:8092/api/v1/oauth2/pre-authorize`
**文件**: `office-flow-backend/src/main/java/com/officeflow/oauth2/controller/OAuth2PreAuthController.java`

**发生的事情**:

1. Office Flow Backend 检查用户是否已登录（通过 cookie 中的 JWT）
2. 如果已登录：直接重定向到授权页面
3. 如果未登录：重定向到 Office Flow Frontend 登录页面

### 4A. 用户已登录 - 直接到授权页面

**重定向到**: `http://localhost:3001/oauth2/authorize?client_id=routine-mail&...`
**文件**: `office-flow-frontend/pages/oauth2/authorize.vue`

**发生的事情**:

1. 显示授权确认页面
2. 显示 Routine Mail 想要访问的权限
3. 用户可以选择"Allow Access"或"Deny"

### 4B. 用户未登录 - 重定向到登录页面

**重定向到**: `http://localhost:3001/login?oauth2_redirect=true&client_id=routine-mail&...`
**文件**: `office-flow-frontend/pages/login/index.vue`

**发生的事情**:

1. 显示 Office Flow 登录页面
2. 页面检测到 OAuth2 参数，知道这是 OAuth2 流程
3. 用户输入 Office Flow 的用户名密码登录
4. 登录成功后，自动重定向到授权页面

### 5. 用户授权

**页面**: `http://localhost:3001/oauth2/authorize`
**API 调用**: `POST /api/v1/oauth2/authorize`

**用户点击"Allow Access"时发生**:

1. 前端发送 POST 请求到 Office Flow Backend
2. Backend 生成授权码（authorization code）
3. 将授权码与用户信息关联存储
4. 返回重定向 URL 给前端

**API 响应**:

```json
{
  "success": true,
  "data": {
    "redirectUrl": "http://localhost:3000/auth/callback?code=abc123&state=xyz789"
  }
}
```

### 6. 重定向回 Routine Mail

**URL**: `http://localhost:3000/auth/callback?code=abc123&state=xyz789`
**文件**: `src/routes/auth/callback/+page.svelte`

**发生的事情**:

1. 页面检测到授权码和 state 参数
2. 自动重定向到服务器端处理：`/api/auth/oauth2/callback`

### 7. 服务器端处理回调

**API**: `GET /api/auth/oauth2/callback`
**文件**: `src/routes/api/auth/oauth2/callback/+server.ts`

**发生的事情**:

1. **验证 state 参数**：确保请求来源合法
2. **交换授权码获取 token**：

   ```javascript
   // 调用Office Flow的token端点
   const tokenResponse = await fetch(
     "http://localhost:8092/api/v1/oauth2/token",
     {
       method: "POST",
       body: new URLSearchParams({
         grant_type: "authorization_code",
         code: authorizationCode,
         redirect_uri: "http://localhost:3000/auth/callback",
         code_verifier: stateData.codeVerifier,
       }),
     }
   );
   ```

3. **获取用户信息**：

   ```javascript
   // 使用access token获取用户信息
   const userResponse = await fetch("http://localhost:8092/api/v1/userinfo", {
     headers: {
       Authorization: `Bearer ${accessToken}`,
     },
   });
   ```

4. **处理用户账户**：

   - 检查 Office Flow 用户是否已经链接到本地账户
   - 如果没有，创建新的本地用户账户
   - 如果有，更新 token 信息

5. **创建本地 JWT session**：

   ```javascript
   const jwtToken = generateJWT({ userId: user.id, email: user.email });
   ```

6. **设置认证 cookie**：

   ```javascript
   cookies.set("auth-token", jwtToken, {
     httpOnly: true,
     secure: process.env.NODE_ENV === "production",
     sameSite: "strict",
   });
   ```

7. **重定向到 dashboard**：
   ```javascript
   return redirect(302, "/dashboard");
   ```

## 🔐 JWT Token 详解

### Office Flow JWT

- **用途**: 用于访问 Office Flow 的 API
- **签名算法**: RS256（RSA 公钥/私钥）
- **存储位置**: Routine Mail 服务器端（数据库）
- **生命周期**: 30 分钟（可刷新）

### Routine Mail JWT

- **用途**: 用于 Routine Mail 的用户 session
- **签名算法**: HS256（HMAC 共享密钥）
- **存储位置**: httpOnly cookie
- **生命周期**: 7 天

### Token 流程

```
Office Flow Access Token → 获取用户信息 → 创建Routine Mail JWT → 用户登录状态
```

## ⚠️ 邮箱冲突处理

### 问题场景

如果 Routine Mail 已有用户 `<EMAIL>`，而 Office Flow 也有同样邮箱的用户，会发生什么？

### 解决方案

当前实现使用 **Office Flow User ID** 作为唯一标识符，而不是邮箱：

```javascript
// 检查是否已经链接
let user = await getUserByOfficeFlowUserId(officeFlowUser.id);

if (!user) {
  // 创建新用户，即使邮箱相同
  user = await createUser({
    email: officeFlowUser.email,
    name: officeFlowUser.name,
    isVerified: true,
  });

  // 创建Office Flow链接
  await createOfficeFlowLink({
    userId: user.id,
    officeFlowUserId: officeFlowUser.id,
    accessToken: token.access_token,
    refreshToken: token.refresh_token,
  });
}
```

### 冲突处理策略

1. **允许重复邮箱**: 系统允许多个用户有相同邮箱
2. **通过 Office Flow ID 区分**: 使用 Office Flow 的用户 ID 作为唯一标识
3. **独立用户账户**: OAuth2 用户和本地注册用户是独立的

### 建议改进

如果需要强制邮箱唯一性，可以修改逻辑：

```javascript
// 检查邮箱是否已存在
const existingUser = await getUserByEmail(officeFlowUser.email);
if (existingUser) {
  // 选项1: 链接到现有账户
  await createOfficeFlowLink({
    userId: existingUser.id,
    officeFlowUserId: officeFlowUser.id,
    // ...
  });

  // 选项2: 返回错误，要求用户手动处理
  // 选项3: 使用不同的邮箱格式（如添加后缀）
}
```

## 🔧 关键配置文件

### Routine Mail 配置

- **OAuth2 客户端配置**: `src/lib/server/oauth2/config.ts`
- **State 管理**: `src/lib/server/oauth2/state.ts`
- **用户数据库操作**: `src/lib/server/db/operations.js`

### Office Flow 配置

- **客户端注册**: `OAuth2AuthorizationServerConfig.java`
- **安全配置**: `SecurityConfig.java` + `OAuth2ResourceServerConfig.java`
- **端点控制器**: `OAuth2AuthorizeController.java` + `OAuth2UserInfoController.java`

## 🚀 测试流程

1. 确保两个服务都在运行：

   - Routine Mail: `http://localhost:3000`
   - Office Flow Frontend: `http://localhost:3001`
   - Office Flow Backend: `http://localhost:8092`

2. 访问 `http://localhost:3000/login`
3. 点击 "Sign in with Office Flow"
4. 完成授权流程
5. 检查是否成功登录到 Routine Mail

## 🔍 调试技巧

### 查看日志

- **Routine Mail**: 控制台输出
- **Office Flow**: `office-flow-backend/docs/logs/logs.txt`

### 常见问题

1. **State 验证失败**: 检查 state 存储和过期时间
2. **JWT 签名错误**: 确保使用正确的签名算法
3. **CORS 错误**: 检查跨域配置
4. **重定向循环**: 检查 URL 配置和认证逻辑

## 📊 API 参考

### Routine Mail APIs

#### 1. 生成授权 URL

```http
POST /api/auth/oauth2/authorize
Content-Type: application/json

{
  "redirectTo": "/dashboard",
  "usePopup": false
}
```

**响应**:

```json
{
  "authorizationUrl": "http://localhost:8092/api/v1/oauth2/pre-authorize?..."
}
```

#### 2. 处理 OAuth2 回调

```http
GET /api/auth/oauth2/callback?code=abc123&state=xyz789
```

**响应**: 重定向到 `/dashboard`

### Office Flow APIs

#### 1. 预认证端点

```http
GET /api/v1/oauth2/pre-authorize?client_id=routine-mail&redirect_uri=...
```

**响应**: 重定向到授权页面或登录页面

#### 2. 授权端点

```http
POST /api/v1/oauth2/authorize
Content-Type: application/json

{
  "clientId": "routine-mail",
  "redirectUri": "http://localhost:3000/auth/callback",
  "scope": "openid profile email read",
  "state": "abc123...",
  "approved": true
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "redirectUrl": "http://localhost:3000/auth/callback?code=def456&state=abc123"
  }
}
```

#### 3. Token 交换端点

```http
POST /api/v1/oauth2/token
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&
code=def456&
redirect_uri=http://localhost:3000/auth/callback&
code_verifier=xyz789...
```

**响应**:

```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIs...",
  "token_type": "Bearer",
  "expires_in": 1800,
  "refresh_token": "eyJhbGciOiJSUzI1NiIs...",
  "scope": "openid profile email read"
}
```

#### 4. 用户信息端点

```http
GET /api/v1/userinfo
Authorization: Bearer eyJhbGciOiJSUzI1NiIs...
```

**响应**:

```json
{
  "sub": "f202f719-8b71-40f7-9eae-6013391ccd3f",
  "name": "System Administrator",
  "preferred_username": "<EMAIL>",
  "email": "<EMAIL>"
}
```

## 🗄️ 数据库设计

### Routine Mail 数据库表

#### users 表

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY,
  email VARCHAR(255) NOT NULL,
  name VARCHAR(255),
  password VARCHAR(255), -- OAuth2用户为空
  is_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### office_flow_links 表

```sql
CREATE TABLE office_flow_links (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  office_flow_user_id VARCHAR(255) NOT NULL UNIQUE,
  access_token TEXT,
  refresh_token TEXT,
  token_expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### sessions 表

```sql
CREATE TABLE sessions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  token VARCHAR(500) NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Office Flow 数据库表

#### oauth2_authorizations 表

```sql
CREATE TABLE oauth2_authorizations (
  id VARCHAR(255) PRIMARY KEY,
  registered_client_id VARCHAR(255) NOT NULL,
  principal_name VARCHAR(255) NOT NULL,
  authorization_grant_type VARCHAR(255) NOT NULL,
  authorized_scopes TEXT,
  attributes TEXT,
  state VARCHAR(500),
  authorization_code_value TEXT,
  authorization_code_issued_at TIMESTAMP,
  authorization_code_expires_at TIMESTAMP,
  authorization_code_metadata TEXT,
  access_token_value TEXT,
  access_token_issued_at TIMESTAMP,
  access_token_expires_at TIMESTAMP,
  access_token_metadata TEXT,
  access_token_type VARCHAR(255),
  access_token_scopes TEXT,
  refresh_token_value TEXT,
  refresh_token_issued_at TIMESTAMP,
  refresh_token_expires_at TIMESTAMP,
  refresh_token_metadata TEXT,
  oidc_id_token_value TEXT,
  oidc_id_token_issued_at TIMESTAMP,
  oidc_id_token_expires_at TIMESTAMP,
  oidc_id_token_metadata TEXT,
  oidc_id_token_claims TEXT,
  user_code_value TEXT,
  user_code_issued_at TIMESTAMP,
  user_code_expires_at TIMESTAMP,
  user_code_metadata TEXT,
  device_code_value TEXT,
  device_code_issued_at TIMESTAMP,
  device_code_expires_at TIMESTAMP,
  device_code_metadata TEXT
);
```

## 🔒 安全考虑

### 1. PKCE (Proof Key for Code Exchange)

- 使用 `code_verifier` 和 `code_challenge` 防止授权码拦截攻击
- `code_challenge = BASE64URL(SHA256(code_verifier))`

### 2. State 参数

- 防止 CSRF 攻击
- 每次授权请求生成唯一的随机字符串
- 服务器端验证 state 的有效性和过期时间

### 3. JWT 安全

- Office Flow 使用 RS256 签名（非对称加密）
- Routine Mail 使用 HS256 签名（对称加密）
- 所有 JWT 都有过期时间

### 4. Cookie 安全

- 使用 httpOnly cookie 存储 JWT
- 设置 Secure 标志（生产环境）
- 使用 SameSite=Strict 防止 CSRF

### 5. HTTPS 要求

- 生产环境必须使用 HTTPS
- 所有重定向 URL 必须使用 HTTPS

## 🚨 故障排除

### 常见错误及解决方案

#### 1. "Invalid state parameter"

**原因**: State 验证失败
**解决**: 检查 state 存储机制和过期时间

#### 2. "JWT signature verification failed"

**原因**: JWT 签名算法不匹配
**解决**: 确保 Office Flow 使用 RS256，Routine Mail 正确验证

#### 3. "CORS policy error"

**原因**: 跨域请求被阻止
**解决**: 检查 CORS 配置，确保允许正确的 origin

#### 4. "User not authenticated"

**原因**: 用户 session 过期或无效
**解决**: 重新登录或刷新 token

#### 5. "Client not found"

**原因**: OAuth2 客户端配置错误
**解决**: 检查 client_id 和 redirect_uri 配置

### 调试命令

#### 检查 JWT 内容

```bash
# 解码JWT (不验证签名)
echo "eyJhbGciOiJSUzI1NiIs..." | base64 -d
```

#### 检查网络请求

```bash
# 使用curl测试API
curl -X POST http://localhost:8092/api/v1/oauth2/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=authorization_code&code=abc123&..."
```

#### 查看数据库状态

```sql
-- 检查OAuth2授权记录
SELECT * FROM oauth2_authorizations WHERE principal_name = '<EMAIL>';

-- 检查用户链接
SELECT * FROM office_flow_links WHERE office_flow_user_id = 'user-id';
```

---

这个 OAuth2 SSO 实现提供了安全、可靠的单点登录体验，同时保持了两个系统的独立性。通过理解这个流程，您可以更好地维护和扩展这个认证系统。
