<script lang="ts">
  import { goto } from '$app/navigation';

  let email = '';
  let newPassword = '';
  let confirmPassword = '';
  let otpCode = '';
  let loading = false;
  let error = '';
  let success = '';
  let requestId = '';
  let step: 'email' | 'otp' | 'password' = 'email';
  let remainingTime = 0;
  let cooldownTimer: NodeJS.Timeout | null = null;

  function startCooldownTimer(seconds: number) {
    remainingTime = seconds;
    cooldownTimer = setInterval(() => {
      remainingTime--;
      if (remainingTime <= 0) {
        clearInterval(cooldownTimer!);
        cooldownTimer = null;
      }
    }, 1000);
  }

  async function sendResetOTP() {
    if (!email) {
      error = 'Please enter your email address';
      return;
    }

    loading = true;
    error = '';
    success = '';

    try {
      const response = await fetch('/api/auth/forgot-password/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        requestId = data.requestId;
        step = 'otp';
        success = 'Password reset code sent to your email';
        startCooldownTimer(180); // 3 minutes cooldown
      } else {
        error = data.error || 'Failed to send reset code';
        if (data.cooldownRemaining) {
          startCooldownTimer(data.cooldownRemaining);
        }
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }

  async function verifyOTPAndResetPassword() {
    if (!otpCode || !newPassword || !confirmPassword) {
      error = 'Please fill in all fields';
      return;
    }

    if (newPassword !== confirmPassword) {
      error = 'Passwords do not match';
      return;
    }

    if (newPassword.length < 8 || newPassword.length > 16) {
      error = 'Password must be 8-16 characters long';
      return;
    }

    if (!/(?=.*[a-z])(?=.*[A-Z])/.test(newPassword)) {
      error = 'Password must contain both uppercase and lowercase letters';
      return;
    }

    loading = true;
    error = '';

    try {
      const response = await fetch('/api/auth/forgot-password/reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          requestId, 
          code: otpCode,
          newPassword 
        }),
      });

      const data = await response.json();

      if (response.ok) {
        success = 'Password reset successful! Redirecting to login...';
        setTimeout(() => goto('/login'), 2000);
      } else {
        error = data.error || 'Password reset failed';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }

  async function resendOTP() {
    await sendResetOTP();
  }

  function goBackToEmail() {
    step = 'email';
    otpCode = '';
    newPassword = '';
    confirmPassword = '';
    error = '';
    success = '';
  }
</script>

<svelte:head>
  <title>Forgot Password - Routine Mail</title>
</svelte:head>

<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <div class="logo">Routine Mail</div>
      <h1>
        {#if step === 'email'}
          Reset Password
        {:else if step === 'otp'}
          Enter Reset Code
        {:else}
          Set New Password
        {/if}
      </h1>
      <p>
        {#if step === 'email'}
          Enter your email to receive a password reset code
        {:else if step === 'otp'}
          We've sent a 6-digit code to {email}
        {:else}
          Enter your new password
        {/if}
      </p>
    </div>

    {#if error}
      <div class="error-message">{error}</div>
    {/if}

    {#if success}
      <div class="success-message">{success}</div>
    {/if}

    {#if step === 'email'}
      <form on:submit|preventDefault={sendResetOTP} class="auth-form">
        <div class="form-group">
          <label for="email">Email Address</label>
          <input
            id="email"
            type="email"
            bind:value={email}
            placeholder="Enter your email"
            required
            disabled={loading}
          />
        </div>

        <button type="submit" class="auth-btn" disabled={loading || remainingTime > 0}>
          {#if loading}
            Sending...
          {:else if remainingTime > 0}
            Resend in {remainingTime}s
          {:else}
            Send Reset Code
          {/if}
        </button>
      </form>
    {:else if step === 'otp'}
      <form on:submit|preventDefault={verifyOTPAndResetPassword} class="auth-form">
        <div class="form-group">
          <label for="otp">Reset Code</label>
          <input
            id="otp"
            type="text"
            bind:value={otpCode}
            placeholder="Enter 6-digit code"
            maxlength="6"
            required
            disabled={loading}
          />
        </div>

        <div class="form-group">
          <label for="newPassword">New Password</label>
          <input
            id="newPassword"
            type="password"
            bind:value={newPassword}
            placeholder="Enter new password (8-16 characters)"
            required
            disabled={loading}
          />
        </div>

        <div class="form-group">
          <label for="confirmPassword">Confirm Password</label>
          <input
            id="confirmPassword"
            type="password"
            bind:value={confirmPassword}
            placeholder="Confirm new password"
            required
            disabled={loading}
          />
        </div>

        <button type="submit" class="auth-btn" disabled={loading}>
          {loading ? 'Resetting Password...' : 'Reset Password'}
        </button>

        <div class="form-actions">
          <button type="button" class="link-btn" on:click={resendOTP} disabled={loading || remainingTime > 0}>
            {remainingTime > 0 ? `Resend code in ${remainingTime}s` : 'Resend code'}
          </button>
          <button type="button" class="link-btn" on:click={goBackToEmail} disabled={loading}>
            Change email
          </button>
        </div>
      </form>
    {/if}

    <div class="auth-footer">
      <p>Remember your password? <a href="/login">Sign in</a></p>
    </div>
  </div>
</div>

<style>
  :global(body) {
    font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    margin: 0;
    padding: 0;
    min-height: 100vh;
  }

  .auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }

  .auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px;
    padding: 3rem;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .auth-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .logo {
    font-size: 1.75rem;
    font-weight: 800;
    color: #2d3748;
    letter-spacing: -0.025em;
    margin-bottom: 1rem;
  }

  .logo::after {
    content: "";
    display: inline-block;
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #4299e1, #63b3ed);
    border-radius: 50%;
    margin-left: 0.5rem;
    vertical-align: middle;
  }

  h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    margin: 0 0 0.5rem 0;
  }

  .auth-header p {
    color: #718096;
    margin: 0;
  }

  .auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  label {
    font-weight: 500;
    color: #2d3748;
    font-size: 0.875rem;
  }

  input {
    padding: 0.875rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
    background: white;
  }

  input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  input:disabled {
    background: #f7fafc;
    cursor: not-allowed;
  }

  .auth-btn {
    padding: 0.875rem;
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .auth-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .auth-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .error-message {
    background: #fed7d7;
    color: #c53030;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.875rem;
    text-align: center;
  }

  .success-message {
    background: #c6f6d5;
    color: #22543d;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.875rem;
    text-align: center;
  }

  .form-actions {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 1rem;
  }

  .link-btn {
    background: none;
    border: none;
    color: #4299e1;
    font-size: 0.875rem;
    cursor: pointer;
    text-decoration: underline;
    padding: 0;
  }

  .link-btn:hover:not(:disabled) {
    color: #3182ce;
  }

  .link-btn:disabled {
    color: #a0aec0;
    cursor: not-allowed;
    text-decoration: none;
  }

  .auth-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
  }

  .auth-footer p {
    color: #718096;
    margin: 0;
  }

  .auth-footer a {
    color: #4299e1;
    text-decoration: none;
    font-weight: 500;
  }

  .auth-footer a:hover {
    text-decoration: underline;
  }
</style>
