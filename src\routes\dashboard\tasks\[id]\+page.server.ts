import { redirect, error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { getTaskById, getCategoriesByUserId } from '$lib/server/db/operations.js';

export const load: PageServerLoad = async ({ params, locals }) => {
  if (!locals.user) {
    throw redirect(302, '/login');
  }

  try {
    const [task, categories] = await Promise.all([
      getTaskById(params.id, locals.user.id),
      getCategoriesByUserId(locals.user.id)
    ]);

    if (!task) {
      throw error(404, 'Task not found');
    }

    return {
      task,
      categories
    };
  } catch (err) {
    console.error('Task detail load error:', err);
    if (err.status === 404) {
      throw err;
    }
    throw error(500, 'Failed to load task');
  }
};
