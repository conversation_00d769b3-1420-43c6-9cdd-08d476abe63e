import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { consumePendingOfficeFlowUser } from '$lib/server/oauth2/state';
import { createOfficeFlowLink, getUserById } from '$lib/server/db/operations.js';

/**
 * Complete registration by linking pending Office Flow account
 * This endpoint is called after successful user registration when there's a pending Office Flow link
 */
export const POST: RequestHandler = async ({ request }) => {
  try {
    const { pendingState, userId } = await request.json();

    if (!pendingState || !userId) {
      return json({ error: 'Missing required parameters' }, { status: 400 });
    }

    // Retrieve pending Office Flow user info
    const pendingData = consumePendingOfficeFlowUser(pendingState);
    if (!pendingData) {
      return json({
        error: 'Invalid or expired pending state',
        message: 'The Office Flow linking session has expired. Please try signing in with Office Flow again.'
      }, { status: 400 });
    }

    // Verify the user exists
    const user = await getUserById(userId);
    if (!user) {
      return json({ error: 'User not found' }, { status: 404 });
    }

    // Create Office Flow link
    await createOfficeFlowLink({
      userId: user.id,
      officeFlowUserId: pendingData.officeFlowUser.id,
      officeFlowEmail: pendingData.officeFlowUser.email,
      officeFlowName: pendingData.officeFlowUser.name,
      officeFlowAvatar: pendingData.officeFlowUser.avatar,
      officeFlowDepartment: pendingData.officeFlowUser.department,
      officeFlowPosition: pendingData.officeFlowUser.position,
      accessToken: pendingData.tokens.accessToken,
      refreshToken: pendingData.tokens.refreshToken,
      tokenExpiresAt: pendingData.tokens.expiresAt
    });

    console.log(`[Complete Registration] Successfully linked Office Flow account ${pendingData.officeFlowUser.email} to user ${user.email}`);

    return json({
      message: 'Office Flow account linked successfully',
      officeFlowUser: {
        email: pendingData.officeFlowUser.email,
        name: pendingData.officeFlowUser.name,
        department: pendingData.officeFlowUser.department,
        position: pendingData.officeFlowUser.position
      }
    });

  } catch (error) {
    console.error('[Complete Registration] Error:', error);
    return json({ error: 'Failed to complete registration' }, { status: 500 });
  }
};
