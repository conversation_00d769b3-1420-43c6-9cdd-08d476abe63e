import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { verifyJWT } from '$lib/server/auth.js';
import { getUserById, updateUserSettings } from '$lib/server/db/operations.js';

export const GET: RequestHandler = async ({ cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const user = await getUserById(payload.userId);
    if (!user) {
      return json({ error: 'User not found' }, { status: 404 });
    }

    // Return only settings-related fields
    const settings = {
      timezone: user.timezone,
      emailFrequencyDays: user.emailFrequencyDays,
      emailPreviewDays: user.emailPreviewDays,
      dailyEmailCount: user.dailyEmailCount,
      lastEmailDate: user.lastEmailDate
    };

    return json({ settings });
  } catch (error) {
    console.error('Get user settings error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const PUT: RequestHandler = async ({ request, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const updates = await request.json();

    // Validate timezone if provided
    if (updates.timezone !== undefined) {
      // Basic timezone validation - you might want to use a more comprehensive list
      const validTimezones = [
        'Asia/Kuala_Lumpur', 'Asia/Singapore', 'Asia/Jakarta', 'Asia/Bangkok',
        'Asia/Manila', 'Asia/Hong_Kong', 'Asia/Shanghai', 'Asia/Tokyo',
        'UTC', 'America/New_York', 'America/Los_Angeles', 'Europe/London'
      ];
      
      if (!validTimezones.includes(updates.timezone)) {
        return json({ error: 'Invalid timezone' }, { status: 400 });
      }
    }

    // Validate email frequency days
    if (updates.emailFrequencyDays !== undefined) {
      if (!Number.isInteger(updates.emailFrequencyDays) || updates.emailFrequencyDays < 1 || updates.emailFrequencyDays > 30) {
        return json({ error: 'Email frequency days must be between 1 and 30' }, { status: 400 });
      }
    }

    // Validate email preview days
    if (updates.emailPreviewDays !== undefined) {
      if (!Number.isInteger(updates.emailPreviewDays) || updates.emailPreviewDays < 1 || updates.emailPreviewDays > 14) {
        return json({ error: 'Email preview days must be between 1 and 14' }, { status: 400 });
      }
    }

    // Only allow updating specific settings fields
    const allowedUpdates: any = {};
    if (updates.timezone !== undefined) allowedUpdates.timezone = updates.timezone;
    if (updates.emailFrequencyDays !== undefined) allowedUpdates.emailFrequencyDays = updates.emailFrequencyDays;
    if (updates.emailPreviewDays !== undefined) allowedUpdates.emailPreviewDays = updates.emailPreviewDays;

    if (Object.keys(allowedUpdates).length === 0) {
      return json({ error: 'No valid settings to update' }, { status: 400 });
    }

    await updateUserSettings(payload.userId, allowedUpdates);

    // Return updated settings
    const user = await getUserById(payload.userId);
    const settings = {
      timezone: user?.timezone,
      emailFrequencyDays: user?.emailFrequencyDays,
      emailPreviewDays: user?.emailPreviewDays,
      dailyEmailCount: user?.dailyEmailCount,
      lastEmailDate: user?.lastEmailDate
    };

    return json({ settings, message: 'Settings updated successfully' });
  } catch (error) {
    console.error('Update user settings error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
