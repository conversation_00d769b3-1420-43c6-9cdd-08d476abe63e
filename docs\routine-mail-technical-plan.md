# Routine Mail 技术实施计划

## 📋 项目概述
基于 SvelteKit 的智能待办事项系统，核心功能是每日邮件提醒，支持复杂重复规则设置，面向办公人群的商务简约设计。

## 🎯 核心功能特性

### 邮件提醒系统
- **每日邮件发送**：每天早上 6:00am 自动发送（基于用户时区）
- **邮件频率控制**：用户可自定义发送频率（每隔N天提醒接下来X天的内容）
- **邮件数量限制**：每用户每日最多5封邮件
- **邮件内操作**：通过链接直接完成任务（✅）
- **纯HTML邮件**：不使用图片，确保兼容性

### 任务管理功能
- **任务结构**：标题 + 小标题 + 备注（类似 Microsoft To Do）
- **任务优先级**：支持优先级设置，邮件中突出显示重要任务
- **任务分类/标签**：支持分类，邮件中分组显示
- **任务预览**：今天逾期 + 明天任务预览

## 🔄 重复规则系统（分阶段实现）

### 第一阶段 - 基础重复规则
- 每天、每周、每月、每年
- 每周特定几天（周一、周三、周五）
- 每月特定日期（每月15号）

### 第二阶段 - 高级重复规则
- 每月第X个工作日
- 每月最后一天、倒数第X天
- 每月第X个周Y（如每月第三个周二）

### 第三阶段 - 智能重复规则
- 每季度最后一个工作日
- 基于节假日的智能调整
- 项目截止日期前N天提醒

## 🌍 时区和本地化

### 时区处理
- **用户时区设置**：保存在数据库中，默认 UTC+8（马来西亚）
- **邮件发送时间**：基于用户本地时区的6:00am
- **时区一致性**：所有时间计算统一使用用户设置的时区

### 节假日支持
- **API集成**：使用 AbstractAPI Holidays
- **马来西亚节假日**：
  ```
  GET https://holidays.abstractapi.com/v1/
    ?api_key=YOUR_API_KEY
    &country=MY
    &location=Selangor
  ```
- **智能调整**：工作日顺延、节假日前提醒

## 💾 数据库设计

### 核心表结构
```sql
-- 用户表
users (
  id UUID PRIMARY KEY,
  email VARCHAR UNIQUE,
  timezone VARCHAR DEFAULT 'Asia/Kuala_Lumpur',
  email_frequency_days INTEGER DEFAULT 1,
  email_preview_days INTEGER DEFAULT 1,
  daily_email_count INTEGER DEFAULT 0,
  last_email_date DATE,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)

-- 任务表
tasks (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  title VARCHAR NOT NULL,
  subtitle VARCHAR,
  notes TEXT,
  priority INTEGER DEFAULT 0, -- 0: 低, 1: 中, 2: 高
  category_id UUID REFERENCES categories(id),
  due_date TIMESTAMP,
  completed BOOLEAN DEFAULT FALSE,
  completed_at TIMESTAMP,
  recurrence_rule JSONB, -- 存储复杂重复规则
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)

-- 分类表
categories (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  name VARCHAR NOT NULL,
  color VARCHAR DEFAULT '#6B7280',
  created_at TIMESTAMP
)

-- 邮件发送记录
email_logs (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  email_type VARCHAR, -- 'daily_summary', 'reminder'
  sent_at TIMESTAMP,
  task_count INTEGER,
  success BOOLEAN DEFAULT TRUE
)
```

### 数据库索引优化（Drizzle）
```typescript
// 用户相关索引
index('idx_users_email').on(users.email),
index('idx_users_timezone').on(users.timezone),

// 任务相关索引
index('idx_tasks_user_id').on(tasks.userId),
index('idx_tasks_due_date').on(tasks.dueDate),
index('idx_tasks_completed').on(tasks.completed),
index('idx_tasks_user_due').on(tasks.userId, tasks.dueDate),
index('idx_tasks_user_completed').on(tasks.userId, tasks.completed),

// 分类索引
index('idx_categories_user_id').on(categories.userId),

// 邮件日志索引
index('idx_email_logs_user_sent').on(emailLogs.userId, emailLogs.sentAt),
index('idx_email_logs_sent_at').on(emailLogs.sentAt)
```

## 🎨 UI/UX 设计原则

### 设计风格
- **商务简约**：少色彩，专业感
- **直观易用**：操作流程简单明了
- **响应式设计**：桌面优先，手机端优化

### 移动端适配
- **触摸友好**：按钮大小适合手指操作
- **简化界面**：移动端隐藏非必要功能
- **快速操作**：支持滑动完成、长按菜单

### 关键页面设计
1. **任务列表**：卡片式布局，优先级颜色标识
2. **任务创建**：分步骤表单，重复规则可视化设置
3. **个人设置**：时区选择、邮件频率配置
4. **邮件模板**：清晰的任务分组，一键操作按钮

## 🔧 技术实现要点

### 邮件系统
- **发送队列**：使用 cron job 处理每日邮件发送
- **模板引擎**：响应式HTML邮件模板
- **链接安全**：带时效性的签名token防止CSRF
- **失败重试**：邮件发送失败的重试机制

### 重复规则引擎
- **JSON存储**：灵活的规则配置格式
- **计算引擎**：下次执行时间计算算法
- **规则验证**：用户输入的规则合法性检查

### 性能优化
- **查询优化**：合理使用Drizzle索引
- **缓存策略**：节假日数据本地缓存
- **批量处理**：邮件发送批量处理

## 📅 开发里程碑

### MVP阶段（4-6周）
- [ ] 基础任务CRUD
- [ ] 用户认证和设置
- [ ] 简单重复规则
- [ ] 每日邮件发送
- [ ] 响应式UI

### 增强阶段（6-8周）
- [ ] 高级重复规则
- [ ] 邮件内操作
- [ ] 任务分类和优先级
- [ ] 移动端优化

### 完善阶段（4-6周）
- [ ] 智能重复规则
- [ ] 节假日集成
- [ ] 性能优化
- [ ] 用户体验完善

## 🚀 部署和运维

### 环境配置
- **开发环境**：本地 PostgreSQL + SvelteKit dev server
- **生产环境**：云数据库 + 容器化部署
- **邮件服务**：Brevo SMTP 集成

### 监控指标
- 邮件发送成功率
- 用户活跃度
- 任务完成率
- 系统响应时间

---

*本文档将随着开发进度持续更新*
