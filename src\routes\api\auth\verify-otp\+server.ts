import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { validatePassword, hashPassword, generateJWT } from '$lib/server/auth.js';
import { getOTPByRequestId, markOTPAsUsed, incrementOTPAttempts, createUser, createSession } from '$lib/server/db/operations.js';

export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    const { requestId, code, password } = await request.json();

    // Validate input
    if (!requestId || !code || !password) {
      return json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Validate password
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return json({ error: passwordValidation.message }, { status: 400 });
    }

    // Get OTP request
    const otpRequest = await getOTPByRequestId(requestId);
    if (!otpRequest) {
      return json({ error: 'Invalid or expired verification code' }, { status: 400 });
    }

    // Check attempts
    const maxAttempts = parseInt(process.env.OTP_MAX_ATTEMPTS || '3');
    if (otpRequest.attempts >= maxAttempts) {
      return json({ error: 'Maximum attempts exceeded' }, { status: 400 });
    }

    // Check if this is for registration
    if (otpRequest.purpose !== 'registration') {
      return json({ error: 'Invalid verification code' }, { status: 400 });
    }

    // Verify code
    if (otpRequest.code !== code) {
      await incrementOTPAttempts(requestId);
      const remainingAttempts = maxAttempts - (otpRequest.attempts + 1);
      return json({
        error: 'Invalid verification code',
        remainingAttempts
      }, { status: 400 });
    }

    // Hash password and create user
    const hashedPassword = await hashPassword(password);
    const user = await createUser({
      email: otpRequest.email,
      password: hashedPassword,
      isVerified: true
    });

    // Mark OTP as used
    await markOTPAsUsed(requestId);

    // Generate JWT and create session
    const jwtPayload = { userId: user.id, email: user.email };
    const token = generateJWT(jwtPayload);
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

    await createSession({
      userId: user.id,
      token,
      expiresAt
    });

    // Set cookie
    cookies.set('auth-token', token, {
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 // 7 days
    });

    return json({
      message: 'Registration successful',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        isVerified: user.isVerified
      }
    });

  } catch (error) {
    console.error('OTP verification error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
