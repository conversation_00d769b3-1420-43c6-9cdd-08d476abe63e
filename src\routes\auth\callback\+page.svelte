<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';

  let status = 'processing';
  let message = 'Processing OAuth2 callback...';

  onMount(async () => {
    try {
      const code = $page.url.searchParams.get('code');
      const state = $page.url.searchParams.get('state');
      const error = $page.url.searchParams.get('error');

      if (error) {
        status = 'error';
        message = `OAuth2 error: ${error}`;
        setTimeout(() => goto('/login?error=oauth2_error'), 3000);
        return;
      }

      if (!code || !state) {
        status = 'error';
        message = 'Missing required OAuth2 parameters';
        setTimeout(() => goto('/login?error=invalid_request'), 3000);
        return;
      }

      // Let the server handle the callback via GET request
      // This will automatically redirect to the appropriate page
      window.location.href = `/api/auth/oauth2/callback?code=${encodeURIComponent(code)}&state=${encodeURIComponent(state)}`;

    } catch (err) {
      console.error('OAuth2 callback error:', err);
      status = 'error';
      message = 'An unexpected error occurred';
      setTimeout(() => goto('/login?error=callback_failed'), 3000);
    }
  });
</script>

<svelte:head>
  <title>OAuth2 Callback - Routine Mail</title>
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
  <div class="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
    {#if status === 'processing'}
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <h2 class="text-xl font-semibold text-gray-900 mb-2">Processing Login...</h2>
      <p class="text-gray-600">{message}</p>
    {:else if status === 'error'}
      <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </div>
      <h2 class="text-xl font-semibold text-gray-900 mb-2">Login Error</h2>
      <p class="text-gray-600">{message}</p>
      <p class="text-sm text-gray-500 mt-2">Redirecting to login page...</p>
    {/if}
  </div>
</div>

<style>
  :global(body) {
    margin: 0;
    padding: 0;
  }
</style>