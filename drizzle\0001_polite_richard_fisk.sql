CREATE TABLE "office_flow_links" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"office_flow_user_id" text NOT NULL,
	"office_flow_email" text NOT NULL,
	"office_flow_name" text,
	"office_flow_avatar" text,
	"office_flow_department" text,
	"office_flow_position" text,
	"access_token" text,
	"refresh_token" text,
	"token_expires_at" timestamp,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "otp_requests" ADD COLUMN "purpose" text DEFAULT 'registration';--> statement-breakpoint
ALTER TABLE "office_flow_links" ADD CONSTRAINT "office_flow_links_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;