# OAuth2Service - 可移植的OAuth2工具类

这个OAuth2Service是一个独立的、可移植的OAuth2客户端工具类，可以轻松集成到任何Node.js项目中。

## 🚀 特性

- ✅ 完整的OAuth2授权码流程支持
- ✅ PKCE (Proof Key for Code Exchange) 安全机制
- ✅ State参数防CSRF攻击
- ✅ Token交换和刷新
- ✅ 用户信息获取
- ✅ JWT验证支持
- ✅ 环境变量配置
- ✅ 详细的日志记录
- ✅ 错误处理

## 📦 安装和配置

### 1. 复制文件
将 `OAuth2Service.js` 复制到你的项目中。

### 2. 环境变量配置
在你的 `.env` 文件中添加以下配置：

```env
# OAuth2 Provider Configuration
OAUTH2_PROVIDER_NAME=your-provider-name
OAUTH2_CLIENT_ID=your-client-id
OAUTH2_CLIENT_SECRET=your-client-secret
OAUTH2_AUTHORIZATION_URL=https://your-provider.com/oauth2/authorize
OAUTH2_TOKEN_URL=https://your-provider.com/oauth2/token
OAUTH2_USERINFO_URL=https://your-provider.com/oauth2/userinfo
OAUTH2_JWKS_URL=https://your-provider.com/.well-known/jwks.json
OAUTH2_REDIRECT_URI=https://your-app.com/auth/callback
OAUTH2_SCOPES=openid,profile,email
OAUTH2_STATE_EXPIRY_MINUTES=20
OAUTH2_ACCESS_TOKEN_EXPIRY_MINUTES=30
OAUTH2_REFRESH_TOKEN_EXPIRY_DAYS=7
```

## 🔧 使用方法

### 基本使用

```javascript
import { OAuth2Service } from './OAuth2Service.js';

// 使用环境变量创建实例
const oauth2 = new OAuth2Service();

// 或者手动传入配置
const oauth2 = new OAuth2Service({
  clientId: 'your-client-id',
  clientSecret: 'your-client-secret',
  authorizationUrl: 'https://provider.com/oauth2/authorize',
  tokenUrl: 'https://provider.com/oauth2/token',
  userInfoUrl: 'https://provider.com/oauth2/userinfo',
  redirectUri: 'https://your-app.com/callback'
});
```

### 1. 生成授权URL

```javascript
// 生成授权URL
const authData = oauth2.generateAuthorizationUrl({
  redirectTo: '/dashboard',
  usePopup: false
});

console.log('授权URL:', authData.authorizationUrl);
console.log('State:', authData.state);
console.log('Code Verifier:', authData.codeVerifier);

// 存储state和codeVerifier（用于后续验证）
// 你需要实现自己的存储机制（数据库、Redis、文件等）
await storeState(authData.state, {
  codeVerifier: authData.codeVerifier,
  redirectTo: authData.redirectTo,
  expiresAt: authData.expiresAt
});

// 重定向用户到授权URL
response.redirect(authData.authorizationUrl);
```

### 2. 处理回调

```javascript
// 在你的回调端点中
export async function handleCallback(request) {
  const code = request.query.code;
  const state = request.query.state;

  // 验证state参数
  const stateData = await getStoredState(state);
  if (!stateData || stateData.expiresAt < Date.now()) {
    throw new Error('Invalid or expired state');
  }

  try {
    // 交换授权码获取token
    const tokens = await oauth2.exchangeCodeForTokens(code, stateData.codeVerifier);
    
    // 获取用户信息
    const userInfo = await oauth2.getUserInfo(tokens.accessToken);
    
    console.log('用户信息:', userInfo);
    console.log('Tokens:', tokens);

    // 存储tokens和用户信息到数据库
    await saveUserTokens(userInfo.id, tokens);
    
    // 创建用户session
    const sessionToken = createUserSession(userInfo);
    
    // 重定向到目标页面
    return redirect(stateData.redirectTo || '/dashboard');
    
  } catch (error) {
    console.error('OAuth2回调处理失败:', error);
    return redirect('/login?error=oauth2_failed');
  }
}
```

### 3. 刷新Token

```javascript
// 刷新过期的access token
try {
  const newTokens = await oauth2.refreshAccessToken(storedRefreshToken);
  
  // 更新存储的tokens
  await updateUserTokens(userId, newTokens);
  
  console.log('Token刷新成功');
} catch (error) {
  console.error('Token刷新失败:', error);
  // 重定向到登录页面
}
```

### 4. 验证JWT

```javascript
// 验证JWT token（如果provider支持）
const payload = await oauth2.verifyJWT(jwtToken);
if (payload) {
  console.log('JWT验证成功:', payload);
} else {
  console.log('JWT验证失败');
}
```

## 🔌 集成示例

### Express.js 集成

```javascript
import express from 'express';
import { OAuth2Service } from './OAuth2Service.js';

const app = express();
const oauth2 = new OAuth2Service();

// 登录路由
app.get('/auth/login', async (req, res) => {
  const authData = oauth2.generateAuthorizationUrl({
    redirectTo: req.query.redirect || '/dashboard'
  });
  
  // 存储state（这里使用session，你可以用数据库）
  req.session.oauth2State = {
    state: authData.state,
    codeVerifier: authData.codeVerifier,
    redirectTo: authData.redirectTo,
    expiresAt: authData.expiresAt
  };
  
  res.redirect(authData.authorizationUrl);
});

// 回调路由
app.get('/auth/callback', async (req, res) => {
  const { code, state } = req.query;
  const stateData = req.session.oauth2State;
  
  if (!stateData || stateData.state !== state) {
    return res.redirect('/login?error=invalid_state');
  }
  
  try {
    const tokens = await oauth2.exchangeCodeForTokens(code, stateData.codeVerifier);
    const userInfo = await oauth2.getUserInfo(tokens.accessToken);
    
    // 创建用户session
    req.session.user = userInfo;
    req.session.tokens = tokens;
    
    res.redirect(stateData.redirectTo);
  } catch (error) {
    res.redirect('/login?error=oauth2_failed');
  }
});
```

### SvelteKit 集成

```javascript
// src/routes/api/auth/login/+server.js
import { OAuth2Service } from '$lib/server/oauth2/OAuth2Service.js';
import { redirect } from '@sveltejs/kit';

const oauth2 = new OAuth2Service();

export async function GET({ url }) {
  const redirectTo = url.searchParams.get('redirect') || '/dashboard';
  
  const authData = oauth2.generateAuthorizationUrl({ redirectTo });
  
  // 存储state到数据库或其他持久化存储
  await storeState(authData.state, authData);
  
  throw redirect(302, authData.authorizationUrl);
}
```

## 🛠️ 自定义配置

你可以通过传入配置对象来覆盖环境变量：

```javascript
const oauth2 = new OAuth2Service({
  providerName: 'custom-provider',
  clientId: 'custom-client-id',
  clientSecret: 'custom-secret',
  authorizationUrl: 'https://custom.com/oauth2/authorize',
  tokenUrl: 'https://custom.com/oauth2/token',
  userInfoUrl: 'https://custom.com/oauth2/userinfo',
  redirectUri: 'https://myapp.com/callback',
  scopes: ['read', 'write', 'admin'],
  stateExpiryMinutes: 30,
  accessTokenExpiryMinutes: 60,
  refreshTokenExpiryDays: 30
});
```

## 🔍 调试

OAuth2Service内置了详细的日志记录。所有重要操作都会输出到控制台：

```
[OAuth2Service] Initialized for provider: office-flow
[OAuth2Service] Generated authorization URL for state: abc123...
[OAuth2Service] Exchanging authorization code for tokens
[OAuth2Service] Successfully exchanged code for tokens
[OAuth2Service] Fetching user info
[OAuth2Service] Successfully fetched user info for user: <EMAIL>
```

## 📝 注意事项

1. **State存储**：你需要实现自己的state存储机制（数据库、Redis、文件等）
2. **Token存储**：安全地存储access token和refresh token
3. **错误处理**：根据你的应用需求实现适当的错误处理
4. **HTTPS**：生产环境中必须使用HTTPS
5. **安全性**：妥善保管client secret，不要暴露给前端

## 🚀 扩展

这个OAuth2Service可以轻松扩展以支持：
- 多个OAuth2 provider
- 自定义JWT验证逻辑
- 更复杂的错误处理
- 自定义日志记录
- Token缓存机制
