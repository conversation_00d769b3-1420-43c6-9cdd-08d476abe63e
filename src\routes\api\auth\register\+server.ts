import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { validateEmail, validatePassword, hashPassword, generateOTPCode } from '$lib/server/auth.js';
import { getUserByEmail, createOTPRequest, generateRequestId, getEmailLimit, createOrUpdateEmailLimit, getLatestOTPForEmail } from '$lib/server/db/operations.js';
import { sendOTPEmail } from '$lib/server/email.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { email } = await request.json();

    // Validate input
    if (!email || !validateEmail(email)) {
      return json({ error: 'Invalid email address' }, { status: 400 });
    }

    // Check if user already exists
    const existingUser = await getUserByEmail(email);
    if (existingUser) {
      return json({ error: 'User already exists' }, { status: 400 });
    }

    // Check email limits
    const emailLimit = await getEmailLimit(email);
    const dailyLimit = parseInt(process.env.DAILY_EMAIL_LIMIT || '5');

    if (emailLimit) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const lastRequestDate = new Date(emailLimit.lastRequestDate);
      lastRequestDate.setHours(0, 0, 0, 0);

      if (lastRequestDate.getTime() === today.getTime() && emailLimit.requestCount >= dailyLimit) {
        return json({ error: 'Daily email limit exceeded' }, { status: 429 });
      }
    }

    // Check cooldown period
    const latestOTP = await getLatestOTPForEmail(email);
    if (latestOTP) {
      const cooldownMinutes = parseInt(process.env.OTP_RESEND_COOLDOWN_MINUTES || '3');
      const cooldownTime = new Date(latestOTP.createdAt.getTime() + cooldownMinutes * 60 * 1000);

      if (new Date() < cooldownTime) {
        const remainingTime = Math.ceil((cooldownTime.getTime() - new Date().getTime()) / 1000);
        return json({
          error: 'Please wait before requesting another code',
          remainingTime
        }, { status: 429 });
      }
    }

    // Generate OTP
    const requestId = generateRequestId();
    const code = generateOTPCode();
    const expiresAt = new Date(Date.now() + parseInt(process.env.OTP_EXPIRES_IN_MINUTES || '5') * 60 * 1000);

    // Save OTP request
    await createOTPRequest({
      requestId,
      email,
      userId: null,
      code,
      purpose: 'registration',
      expiresAt
    });

    // Update email limit
    await createOrUpdateEmailLimit(email);

    // Send OTP email
    const emailSent = await sendOTPEmail({
      to: email,
      code,
      type: 'registration'
    });

    if (!emailSent) {
      return json({ error: 'Failed to send verification email' }, { status: 500 });
    }

    return json({
      message: 'Verification code sent to your email',
      requestId
    });

  } catch (error) {
    console.error('Registration error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
