import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { getTasksByUserId, getTasksDueToday, getOverdueTasks, getCategoriesByUserId } from '$lib/server/db/operations.js';

export const load: PageServerLoad = async ({ locals }) => {
  if (!locals.user) {
    throw redirect(302, '/login');
  }

  try {
    // Load user's tasks and categories
    const [allTasks, todayTasks, overdueTasks, categories] = await Promise.all([
      getTasksByUserId(locals.user.id),
      getTasksDueToday(locals.user.id),
      getOverdueTasks(locals.user.id),
      getCategoriesByUserId(locals.user.id)
    ]);

    // Calculate task groups
    const completedTasks = allTasks.filter(task => task.completed);
    const activeTasks = allTasks.filter(task => !task.completed);

    return {
      tasks: {
        all: allTasks,
        today: todayTasks,
        overdue: overdueTasks,
        active: activeTasks,
        completed: completedTasks
      },
      categories
    };
  } catch (error) {
    console.error('Tasks page load error:', error);
    // Return empty data on error
    return {
      tasks: {
        all: [],
        today: [],
        overdue: [],
        active: [],
        completed: []
      },
      categories: []
    };
  }
};
