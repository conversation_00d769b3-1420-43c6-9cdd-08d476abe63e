import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { oauth2Service } from '$lib/server/oauth2/OAuth2Service.js';
import { storeState } from '$lib/server/oauth2/state';

/**
 * Generate OAuth2 authorization URL for login using OAuth2Service
 */
export const POST: RequestHandler = async ({ request }) => {
  try {
    const { redirectTo, usePopup } = await request.json();

    // 使用OAuth2Service生成授权URL和相关参数
    const authData = oauth2Service.generateAuthorizationUrl({
      redirectTo: redirectTo || '/dashboard',
      usePopup: usePopup || false
    });

    // 存储state数据
    storeState({
      state: authData.state,
      codeVerifier: authData.codeVerifier,
      redirectTo: authData.redirectTo,
      usePopup: authData.usePopup,
      timestamp: authData.timestamp
    });

    return json({ authorizationUrl: authData.authorizationUrl });

  } catch (error) {
    console.error('[OAuth2Service] Failed to generate authorization URL:', error);
    return json({ error: 'Failed to generate authorization URL' }, { status: 500 });
  }
};
