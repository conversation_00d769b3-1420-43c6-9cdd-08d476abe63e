/**
 * Client-side OAuth2 utilities for Office Flow integration
 */

/**
 * Generate OAuth2 authorization URL for login
 */
export async function generateOfficeFlowLoginUrl(redirectTo?: string, usePopup?: boolean): Promise<string> {
  const response = await fetch('/api/auth/oauth2/authorize', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ redirectTo, usePopup })
  });

  if (!response.ok) {
    throw new Error('Failed to generate authorization URL');
  }

  const data = await response.json();
  return data.authorizationUrl;
}

/**
 * Generate OAuth2 authorization URL for account linking
 */
export async function generateOfficeFlowLinkUrl(redirectTo?: string): Promise<string> {
  const response = await fetch('/api/auth/oauth2/link', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ redirectTo })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to generate authorization URL');
  }

  const data = await response.json();
  return data.authorizationUrl;
}

/**
 * Get Office Flow link status
 */
export async function getOfficeFlowLinkStatus(): Promise<{
  isLinked: boolean;
  officeFlowUser?: {
    id: string;
    email: string;
    name: string;
    avatar?: string;
    department?: string;
    position?: string;
  };
  linkedAt?: string;
  lastUpdated?: string;
}> {
  const response = await fetch('/api/auth/oauth2/link');

  if (!response.ok) {
    throw new Error('Failed to get link status');
  }

  return await response.json();
}

/**
 * Unlink Office Flow account
 */
export async function unlinkOfficeFlowAccount(): Promise<void> {
  const response = await fetch('/api/auth/oauth2/link', {
    method: 'DELETE'
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to unlink account');
  }
}

/**
 * Open Office Flow login in popup window
 */
export async function openOfficeFlowLoginPopup(redirectTo?: string): Promise<void> {
  try {
    const authUrl = await generateOfficeFlowLoginUrl(redirectTo, true);

    // Calculate popup window position (center of screen)
    const width = 500;
    const height = 600;
    const left = (screen.width - width) / 2;
    const top = (screen.height - height) / 2;

    // Open popup window
    const popup = window.open(
      authUrl,
      'office-flow-oauth',
      `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
    );

    if (!popup) {
      throw new Error('Popup blocked. Please allow popups for this site.');
    }

    // Monitor popup for completion
    return new Promise((resolve, reject) => {
      // Listen for messages from popup window
      const messageHandler = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) {
          return; // Ignore messages from other origins
        }

        if (event.data.type === 'oauth2-success') {
          clearInterval(checkClosed);
          window.removeEventListener('message', messageHandler);
          popup.close();
          resolve();
          // Reload page to update authentication state
          window.location.reload();
        } else if (event.data.type === 'oauth2-error') {
          clearInterval(checkClosed);
          window.removeEventListener('message', messageHandler);
          popup.close();
          reject(new Error(event.data.error || 'OAuth2 login failed'));
        }
      };

      window.addEventListener('message', messageHandler);

      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          window.removeEventListener('message', messageHandler);
          reject(new Error('OAuth2 login was cancelled'));
        }
      }, 1000);

      // Timeout after 5 minutes
      setTimeout(() => {
        clearInterval(checkClosed);
        if (!popup.closed) {
          popup.close();
        }
        reject(new Error('OAuth2 login timeout'));
      }, 5 * 60 * 1000);
    });

  } catch (error) {
    console.error('Failed to open Office Flow login popup:', error);
    throw error;
  }
}

/**
 * Redirect to Office Flow login (fallback for popup-blocked scenarios)
 */
export async function redirectToOfficeFlowLogin(redirectTo?: string): Promise<void> {
  try {
    // Use redirect mode (not popup) to avoid conflicts
    const authUrl = await generateOfficeFlowLoginUrl(redirectTo, false);
    window.location.href = authUrl;
  } catch (error) {
    console.error('Failed to redirect to Office Flow login:', error);
    throw error;
  }
}

/**
 * Redirect to Office Flow account linking
 */
export async function redirectToOfficeFlowLink(redirectTo?: string): Promise<void> {
  try {
    const authUrl = await generateOfficeFlowLinkUrl(redirectTo);
    window.location.href = authUrl;
  } catch (error) {
    console.error('Failed to redirect to Office Flow linking:', error);
    throw error;
  }
}
