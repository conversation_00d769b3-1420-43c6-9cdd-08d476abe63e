/**
 * PKCE (Proof Key for Code Exchange) utilities for OAuth2
 * Implements RFC 7636 for secure OAuth2 flows
 */

import { createHash, randomBytes } from 'crypto';

/**
 * Generate a cryptographically secure random string
 */
function generateRandomString(length: number): string {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  const randomValues = randomBytes(length);
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += charset[randomValues[i] % charset.length];
  }
  
  return result;
}

/**
 * Generate PKCE code verifier
 * Must be 43-128 characters long and contain only unreserved characters
 */
export function generateCodeVerifier(): string {
  return generateRandomString(128);
}

/**
 * Generate PKCE code challenge from code verifier
 * Uses SHA256 hash and base64url encoding
 */
export function generateCodeChallenge(codeVerifier: string): string {
  const hash = createHash('sha256').update(codeVerifier).digest();
  return hash.toString('base64url');
}

/**
 * Generate both code verifier and challenge
 */
export function generatePKCEPair(): { codeVerifier: string; codeChallenge: string } {
  const codeVerifier = generateCodeVerifier();
  const codeChallenge = generateCodeChallenge(codeVerifier);
  
  return {
    codeVerifier,
    codeChallenge
  };
}

/**
 * Verify PKCE code verifier against challenge
 */
export function verifyPKCE(codeVerifier: string, codeChallenge: string): boolean {
  const computedChallenge = generateCodeChallenge(codeVerifier);
  return computedChallenge === codeChallenge;
}
