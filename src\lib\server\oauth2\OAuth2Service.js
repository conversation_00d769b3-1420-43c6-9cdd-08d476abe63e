import crypto from 'crypto';
import { env } from '$env/dynamic/private';

/**
 * OAuth2Service - 可移植的OAuth2工具类
 * 
 * 功能包括：
 * - 授权URL生成
 * - Token交换
 * - 用户信息获取
 * - Token刷新
 * - State管理
 * - PKCE处理
 * - JWT验证
 */
export class OAuth2Service {
  constructor(config = {}) {
    // 从环境变量或传入的config中获取配置
    this.config = {
      providerName: config.providerName || env.OAUTH2_PROVIDER_NAME || 'oauth2-provider',
      clientId: config.clientId || env.OAUTH2_CLIENT_ID,
      clientSecret: config.clientSecret || env.OAUTH2_CLIENT_SECRET,
      authorizationUrl: config.authorizationUrl || env.OAUTH2_AUTHORIZATION_URL,
      tokenUrl: config.tokenUrl || env.OAUTH2_TOKEN_URL,
      userInfoUrl: config.userInfoUrl || env.OAUTH2_USERINFO_URL,
      jwksUrl: config.jwksUrl || env.OAUTH2_JWKS_URL,
      redirectUri: config.redirectUri || env.OAUTH2_REDIRECT_URI,
      scopes: config.scopes || env.OAUTH2_SCOPES?.split(',') || ['openid', 'profile', 'email'],
      stateExpiryMinutes: parseInt(config.stateExpiryMinutes || env.OAUTH2_STATE_EXPIRY_MINUTES || '20'),
      accessTokenExpiryMinutes: parseInt(config.accessTokenExpiryMinutes || env.OAUTH2_ACCESS_TOKEN_EXPIRY_MINUTES || '30'),
      refreshTokenExpiryDays: parseInt(config.refreshTokenExpiryDays || env.OAUTH2_REFRESH_TOKEN_EXPIRY_DAYS || '7')
    };

    // 验证必需的配置
    this.validateConfig();
    
    console.log(`[OAuth2Service] Initialized for provider: ${this.config.providerName}`);
  }

  /**
   * 验证配置
   */
  validateConfig() {
    const required = ['clientId', 'clientSecret', 'authorizationUrl', 'tokenUrl', 'redirectUri'];
    const missing = required.filter(key => !this.config[key]);
    
    if (missing.length > 0) {
      throw new Error(`[OAuth2Service] Missing required configuration: ${missing.join(', ')}`);
    }
  }

  /**
   * 生成PKCE参数
   * @returns {Object} { codeVerifier, codeChallenge }
   */
  generatePKCE() {
    const codeVerifier = crypto.randomBytes(32).toString('base64url');
    const codeChallenge = crypto
      .createHash('sha256')
      .update(codeVerifier)
      .digest('base64url');

    return { codeVerifier, codeChallenge };
  }

  /**
   * 生成随机state参数
   * @returns {string} state
   */
  generateState() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 生成授权URL
   * @param {Object} options - 选项
   * @param {string} options.redirectTo - 授权后重定向的页面
   * @param {boolean} options.usePopup - 是否使用弹窗模式
   * @param {string} options.state - 自定义state参数
   * @param {Object} options.pkce - 自定义PKCE参数
   * @returns {Object} { authorizationUrl, state, codeVerifier }
   */
  generateAuthorizationUrl(options = {}) {
    const { redirectTo = '/dashboard', usePopup = false, state, pkce } = options;

    // 生成或使用提供的参数
    const stateParam = state || this.generateState();
    const pkceParams = pkce || this.generatePKCE();

    // 构建授权URL
    const authUrl = new URL(this.config.authorizationUrl);
    authUrl.searchParams.set('client_id', this.config.clientId);
    authUrl.searchParams.set('redirect_uri', this.config.redirectUri);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('scope', this.config.scopes.join(' '));
    authUrl.searchParams.set('state', stateParam);
    authUrl.searchParams.set('code_challenge', pkceParams.codeChallenge);
    authUrl.searchParams.set('code_challenge_method', 'S256');

    console.log(`[OAuth2Service] Generated authorization URL for state: ${stateParam}`);

    return {
      authorizationUrl: authUrl.toString(),
      state: stateParam,
      codeVerifier: pkceParams.codeVerifier,
      redirectTo,
      usePopup,
      timestamp: Date.now(),
      expiresAt: Date.now() + (this.config.stateExpiryMinutes * 60 * 1000)
    };
  }

  /**
   * 交换授权码获取token
   * @param {string} code - 授权码
   * @param {string} codeVerifier - PKCE code verifier
   * @returns {Promise<Object>} token响应
   */
  async exchangeCodeForTokens(code, codeVerifier) {
    console.log(`[OAuth2Service] Exchanging authorization code for tokens`);

    const tokenData = new URLSearchParams({
      grant_type: 'authorization_code',
      code: code,
      redirect_uri: this.config.redirectUri,
      code_verifier: codeVerifier
    });

    const response = await fetch(this.config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${this.config.clientId}:${this.config.clientSecret}`).toString('base64')}`
      },
      body: tokenData
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[OAuth2Service] Token exchange failed:`, errorText);
      throw new Error(`Token exchange failed: ${response.status} ${response.statusText}`);
    }

    const tokens = await response.json();
    console.log(`[OAuth2Service] Successfully exchanged code for tokens`);

    return {
      accessToken: tokens.access_token,
      refreshToken: tokens.refresh_token,
      tokenType: tokens.token_type || 'Bearer',
      expiresIn: tokens.expires_in || this.config.accessTokenExpiryMinutes * 60,
      scope: tokens.scope,
      expiresAt: new Date(Date.now() + (tokens.expires_in || this.config.accessTokenExpiryMinutes * 60) * 1000)
    };
  }

  /**
   * 获取用户信息
   * @param {string} accessToken - 访问令牌
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo(accessToken) {
    console.log(`[OAuth2Service] Fetching user info`);

    const response = await fetch(this.config.userInfoUrl, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[OAuth2Service] User info fetch failed:`, errorText);
      throw new Error(`User info fetch failed: ${response.status} ${response.statusText}`);
    }

    const userInfo = await response.json();
    console.log(`[OAuth2Service] Successfully fetched user info for user: ${userInfo.email || userInfo.sub}`);

    return {
      id: userInfo.sub,
      email: userInfo.email || userInfo.preferred_username,
      name: userInfo.name,
      username: userInfo.preferred_username,
      raw: userInfo // 保留原始数据
    };
  }

  /**
   * 刷新访问令牌
   * @param {string} refreshToken - 刷新令牌
   * @returns {Promise<Object>} 新的token信息
   */
  async refreshAccessToken(refreshToken) {
    console.log(`[OAuth2Service] Refreshing access token`);

    const tokenData = new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: refreshToken
    });

    const response = await fetch(this.config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${this.config.clientId}:${this.config.clientSecret}`).toString('base64')}`
      },
      body: tokenData
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[OAuth2Service] Token refresh failed:`, errorText);
      throw new Error(`Token refresh failed: ${response.status} ${response.statusText}`);
    }

    const tokens = await response.json();
    console.log(`[OAuth2Service] Successfully refreshed access token`);

    return {
      accessToken: tokens.access_token,
      refreshToken: tokens.refresh_token || refreshToken, // 有些provider不返回新的refresh token
      tokenType: tokens.token_type || 'Bearer',
      expiresIn: tokens.expires_in || this.config.accessTokenExpiryMinutes * 60,
      scope: tokens.scope,
      expiresAt: new Date(Date.now() + (tokens.expires_in || this.config.accessTokenExpiryMinutes * 60) * 1000)
    };
  }

  /**
   * 验证JWT token（如果provider支持）
   * @param {string} token - JWT token
   * @returns {Promise<Object|null>} 解码后的payload或null
   */
  async verifyJWT(token) {
    if (!this.config.jwksUrl) {
      console.warn(`[OAuth2Service] JWKS URL not configured, skipping JWT verification`);
      return null;
    }

    try {
      console.log(`[OAuth2Service] Verifying JWT token`);
      
      // 这里可以实现JWT验证逻辑
      // 为了简化，暂时只解码不验证签名
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
      
      // 检查过期时间
      if (payload.exp && payload.exp < Date.now() / 1000) {
        throw new Error('JWT token expired');
      }

      console.log(`[OAuth2Service] JWT token verified successfully`);
      return payload;
    } catch (error) {
      console.error(`[OAuth2Service] JWT verification failed:`, error.message);
      return null;
    }
  }

  /**
   * 获取配置信息（用于调试）
   * @returns {Object} 配置信息（不包含敏感信息）
   */
  getConfig() {
    const { clientSecret, ...safeConfig } = this.config;
    return {
      ...safeConfig,
      clientSecret: '***' // 隐藏敏感信息
    };
  }
}

// 创建默认实例
export const oauth2Service = new OAuth2Service();

// 导出配置常量
export const OAUTH2_ERRORS = {
  INVALID_CONFIG: 'INVALID_CONFIG',
  TOKEN_EXCHANGE_FAILED: 'TOKEN_EXCHANGE_FAILED',
  USER_INFO_FAILED: 'USER_INFO_FAILED',
  TOKEN_REFRESH_FAILED: 'TOKEN_REFRESH_FAILED',
  JWT_VERIFICATION_FAILED: 'JWT_VERIFICATION_FAILED'
};
