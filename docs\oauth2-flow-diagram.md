# OAuth2 流程图解

## 🎯 简化流程图

```
用户浏览器 → Routine Mail → Office Flow Backend → Office Flow Frontend → 用户授权 → 回调处理 → 登录成功
```

## 📋 详细步骤图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户浏览器    │    │  Routine Mail   │    │ Office Flow BE  │    │ Office Flow FE  │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │                       │
         │ 1. 点击登录按钮        │                       │                       │
         ├──────────────────────►│                       │                       │
         │                       │                       │                       │
         │                       │ 2. 生成授权URL        │                       │
         │                       │ POST /api/auth/oauth2/│                       │
         │                       │      authorize        │                       │
         │                       │                       │                       │
         │ 3. 重定向到Office Flow │                       │                       │
         │    预认证端点          │                       │                       │
         ├───────────────────────────────────────────────►│                       │
         │                       │                       │                       │
         │                       │                       │ 4. 检查用户登录状态  │
         │                       │                       │                       │
         │                       │                       │ 5a. 已登录: 重定向到 │
         │                       │                       │     授权页面          │
         │                       │                       ├──────────────────────►│
         │                       │                       │                       │
         │                       │                       │ 5b. 未登录: 重定向到 │
         │                       │                       │     登录页面          │
         │                       │                       ├──────────────────────►│
         │                       │                       │                       │
         │ 6. 显示授权/登录页面   │                       │                       │
         ├───────────────────────────────────────────────────────────────────────┤
         │                       │                       │                       │
         │ 7. 用户登录/授权       │                       │                       │
         ├───────────────────────────────────────────────────────────────────────►│
         │                       │                       │                       │
         │                       │                       │ 8. 处理授权请求      │
         │                       │                       │ POST /api/v1/oauth2/ │
         │                       │                       │      authorize        │
         │                       │                       ├──────────────────────►│
         │                       │                       │                       │
         │                       │                       │ 9. 生成授权码        │
         │                       │                       │    返回重定向URL      │
         │                       │                       │◄──────────────────────┤
         │                       │                       │                       │
         │ 10. 重定向回Routine Mail│                      │                       │
         │     带授权码           │                       │                       │
         ├──────────────────────►│                       │                       │
         │                       │                       │                       │
         │                       │ 11. 交换授权码获取token│                      │
         │                       │ POST /api/v1/oauth2/  │                       │
         │                       │      token            │                       │
         │                       ├──────────────────────►│                       │
         │                       │                       │                       │
         │                       │ 12. 返回access_token  │                       │
         │                       │◄──────────────────────┤                       │
         │                       │                       │                       │
         │                       │ 13. 获取用户信息      │                       │
         │                       │ GET /api/v1/userinfo  │                       │
         │                       ├──────────────────────►│                       │
         │                       │                       │                       │
         │                       │ 14. 返回用户信息      │                       │
         │                       │◄──────────────────────┤                       │
         │                       │                       │                       │
         │                       │ 15. 创建本地用户      │                       │
         │                       │     生成本地JWT       │                       │
         │                       │     设置cookie        │                       │
         │                       │                       │                       │
         │ 16. 重定向到dashboard  │                       │                       │
         │◄──────────────────────┤                       │                       │
         │                       │                       │                       │
```

## 🔑 关键概念解释

### 1. 授权码流程 (Authorization Code Flow)
这是OAuth2最安全的流程，适用于有后端服务器的应用：
- 前端获得授权码（短期有效）
- 后端用授权码交换访问令牌（长期有效）
- 访问令牌不会暴露给前端

### 2. PKCE (Proof Key for Code Exchange)
增强安全性的机制：
```
code_verifier = 随机生成的字符串
code_challenge = BASE64URL(SHA256(code_verifier))

授权请求: 发送 code_challenge
令牌交换: 发送 code_verifier
服务器验证: SHA256(code_verifier) == code_challenge
```

### 3. State参数
防止CSRF攻击：
```
1. 生成随机state
2. 存储在服务器端
3. 授权请求时发送state
4. 回调时验证state
```

## 🌐 URL流转过程

### 1. 初始登录URL
```
http://localhost:3000/login
```

### 2. 授权URL生成
```
http://localhost:8092/api/v1/oauth2/pre-authorize?
  client_id=routine-mail&
  redirect_uri=http://localhost:3000/auth/callback&
  response_type=code&
  scope=openid+profile+email+read&
  state=abc123...&
  code_challenge=xyz789...&
  code_challenge_method=S256
```

### 3. 授权页面URL
```
http://localhost:3001/oauth2/authorize?
  client_id=routine-mail&
  redirect_uri=http://localhost:3000/auth/callback&
  response_type=code&
  scope=openid+profile+email+read&
  state=abc123...&
  code_challenge=xyz789...&
  code_challenge_method=S256
```

### 4. 回调URL
```
http://localhost:3000/auth/callback?
  code=def456&
  state=abc123...
```

### 5. 最终重定向
```
http://localhost:3000/dashboard
```

## 🔐 Token类型说明

### Office Flow Access Token (RS256)
```json
{
  "alg": "RS256",
  "typ": "JWT"
}
{
  "sub": "f202f719-8b71-40f7-9eae-6013391ccd3f",
  "aud": ["routine-mail"],
  "iss": "http://localhost:8092/api/v1",
  "exp": 1640995200,
  "iat": 1640991600,
  "scope": "openid profile email read"
}
```

### Routine Mail Session Token (HS256)
```json
{
  "alg": "HS256",
  "typ": "JWT"
}
{
  "userId": "local-user-uuid",
  "email": "<EMAIL>",
  "exp": 1641595200,
  "iat": 1640991600
}
```

## 📊 数据流转

### 1. 用户数据映射
```
Office Flow User → Routine Mail User

{
  "id": "office-flow-user-id",     → office_flow_user_id
  "email": "<EMAIL>",     → email
  "name": "User Name",             → name
  "preferred_username": "username" → (可选)
}
```

### 2. Token存储
```
Office Flow Tokens → office_flow_links表
{
  "access_token": "...",   → access_token
  "refresh_token": "...",  → refresh_token
  "expires_in": 1800       → token_expires_at
}

Routine Mail Session → sessions表
{
  "token": "local-jwt",    → token
  "expires_at": "..."      → expires_at
}
```

## ⚡ 性能优化

### 1. Token缓存
- Office Flow tokens存储在数据库
- 避免重复的userinfo请求
- 实现token自动刷新

### 2. State管理
- 使用文件系统存储state（可扩展到Redis）
- 定期清理过期state
- 设置合理的过期时间（20分钟）

### 3. 数据库优化
- 在office_flow_user_id上建立索引
- 定期清理过期的sessions和tokens
- 使用连接池管理数据库连接

## 🔧 配置要点

### Routine Mail配置
```javascript
// src/lib/server/oauth2/config.ts
export const OAUTH2_CONFIG = {
  CLIENT_ID: 'routine-mail',
  CLIENT_SECRET: 'routine-mail-secret',
  AUTHORIZATION_URL: 'http://localhost:8092/api/v1/oauth2/pre-authorize',
  TOKEN_URL: 'http://localhost:8092/api/v1/oauth2/token',
  USERINFO_URL: 'http://localhost:8092/api/v1/userinfo',
  REDIRECT_URI: 'http://localhost:3000/auth/callback',
  SCOPE: 'openid profile email read'
};
```

### Office Flow配置
```java
// OAuth2AuthorizationServerConfig.java
RegisteredClient.withId(UUID.randomUUID().toString())
  .clientId("routine-mail")
  .clientSecret("{noop}routine-mail-secret")
  .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
  .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
  .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
  .redirectUri("http://localhost:3000/auth/callback")
  .scope(OidcScopes.OPENID)
  .scope(OidcScopes.PROFILE)
  .scope(OidcScopes.EMAIL)
  .scope("read")
```

---

这个流程图帮助您理解OAuth2 SSO的完整工作机制，从用户点击登录到最终获得访问权限的每一个步骤。
