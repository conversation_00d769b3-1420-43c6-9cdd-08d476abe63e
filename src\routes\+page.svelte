<div class="landing-page">
  <header class="header">
    <div class="container">
      <div class="logo">Routine Mail</div>
      <nav class="main-nav">
        <a href="/login" class="nav-link">Login</a>
        <a href="/register" class="btn-primary-outline">Get Started</a>
      </nav>
    </div>
  </header>

  <main class="hero-section">
    <div class="container text-center">
      <h1 class="hero-title">
        The Smart To-Do List That Works Through Email
      </h1>
      <p class="hero-subtitle">
        Routine Mail sends you daily task reminders and lets you manage your to-dos directly from your inbox. <br />
        Never miss an important task again.
      </p>
      <div class="hero-actions">
        <a href="/register" class="btn-primary">Create Your Free Account</a>
        <a href="/login" class="btn-secondary">Login to Your Account</a>
      </div>
    </div>
  </main>

  <footer class="footer">
    <div class="container">
      <p>&copy; {new Date().getFullYear()} Routine Mail. All rights reserved.</p>
    </div>
  </footer>
</div>

<style>
  .landing-page {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    background-color: #ffffff;
    color: #212529;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .container {
    width: 100%;
    max-width: 1140px;
    margin: 0 auto;
    padding: 0 1.5rem;
  }

  /* Header */
  .header {
    padding: 1.5rem 0;
    border-bottom: 1px solid #dee2e6;
  }
  .header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .logo {
    font-size: 1.5rem;
    font-weight: 600;
  }
  .main-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  .nav-link {
    color: #495057;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
  }
  .nav-link:hover {
    color: #007bff;
  }

  /* Hero Section */
  .hero-section {
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4rem 0;
  }
  .hero-title {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }
  .hero-subtitle {
    font-size: 1.125rem;
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 2.5rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }
  .hero-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
  }

  /* Buttons */
  .btn-primary, .btn-secondary, .btn-primary-outline {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 1rem;
    border: 1px solid transparent;
    cursor: pointer;
    display: inline-block;
  }
  .btn-primary {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
  }
  .btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
  }
  .btn-secondary {
    background-color: #e9ecef;
    color: #212529;
    border-color: #ced4da;
  }
  .btn-secondary:hover {
    background-color: #dee2e6;
  }
  .btn-primary-outline {
    background-color: transparent;
    color: #007bff;
    border-color: #007bff;
  }
  .btn-primary-outline:hover {
    background-color: #007bff;
    color: white;
  }
  
  /* Footer */
  .footer {
    padding: 2rem 0;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    text-align: center;
    color: #6c757d;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .hero-title {
      font-size: 2.25rem;
    }
    .hero-actions {
      flex-direction: column;
    }
  }
</style>
