import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { deleteSession } from '$lib/server/db/operations.js';

export const POST: RequestHandler = async ({ cookies }) => {
  try {
    const token = cookies.get('auth-token');
    
    if (token) {
      await deleteSession(token);
    }

    // Clear cookie
    cookies.delete('auth-token', { path: '/' });

    return json({ message: 'Logout successful' });

  } catch (error) {
    console.error('Logout error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
