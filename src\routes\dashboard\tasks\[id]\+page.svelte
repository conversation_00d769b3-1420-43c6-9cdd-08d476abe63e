<script lang="ts">
  import { goto } from '$app/navigation';
  import { invalidateAll } from '$app/navigation';
  import type { PageData } from './$types';

  export let data: PageData;

  const { task, categories } = data;

  // Find category for this task
  const category = categories.find(c => c.id === task.categoryId);

  function formatDate(date: string | null): string {
    if (!date) return 'No due date';
    
    const taskDate = new Date(date);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const taskDay = new Date(taskDate.getFullYear(), taskDate.getMonth(), taskDate.getDate());
    
    const diffTime = taskDay.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    const timeStr = taskDate.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
    
    if (diffDays === 0) {
      return `Today at ${timeStr}`;
    } else if (diffDays === 1) {
      return `Tomorrow at ${timeStr}`;
    } else if (diffDays === -1) {
      return `Yesterday at ${timeStr}`;
    } else if (diffDays > 0) {
      return `${taskDate.toLocaleDateString()} at ${timeStr}`;
    } else {
      return `${Math.abs(diffDays)} days overdue (${taskDate.toLocaleDateString()} at ${timeStr})`;
    }
  }

  function getPriorityLabel(priority: number): string {
    switch (priority) {
      case 2: return 'High';
      case 1: return 'Normal';
      default: return 'Low';
    }
  }

  function getPriorityColor(priority: number): string {
    switch (priority) {
      case 2: return '#dc2626';
      case 1: return '#059669';
      default: return '#64748b';
    }
  }

  async function toggleComplete() {
    try {
      const endpoint = task.completed ? `/api/tasks/${task.id}` : `/api/tasks/${task.id}/complete`;
      const method = task.completed ? 'PUT' : 'POST';
      const body = task.completed ? JSON.stringify({ completed: false, completedAt: null }) : undefined;

      const response = await fetch(endpoint, {
        method,
        headers: task.completed ? { 'Content-Type': 'application/json' } : {},
        body
      });

      if (response.ok) {
        await invalidateAll();
      } else {
        console.error('Failed to toggle task completion');
      }
    } catch (error) {
      console.error('Error toggling task completion:', error);
    }
  }

  async function deleteTask() {
    if (!confirm('Are you sure you want to delete this task? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/tasks/${task.id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        goto('/dashboard/tasks');
      } else {
        console.error('Failed to delete task');
      }
    } catch (error) {
      console.error('Error deleting task:', error);
    }
  }
</script>

<svelte:head>
  <title>{task.title} - Routine Mail</title>
</svelte:head>

<div class="page-header">
  <div class="header-content">
    <div class="breadcrumb">
      <a href="/dashboard">Dashboard</a>
      <span>›</span>
      <a href="/dashboard/tasks">Tasks</a>
      <span>›</span>
      <span class="current">{task.title}</span>
    </div>
    <div class="header-actions">
      <a href="/dashboard/tasks/{task.id}/edit" class="btn-secondary">Edit Task</a>
      <button class="btn-danger" on:click={deleteTask}>Delete</button>
    </div>
  </div>
</div>

<div class="task-detail-container">
  <div class="task-card">
    <div class="task-header">
      <div class="task-status">
        <input
          type="checkbox"
          class="task-check"
          checked={task.completed}
          on:change={toggleComplete}
        />
        <h1 class="task-title" class:completed={task.completed}>{task.title}</h1>
      </div>
      
      <div class="task-meta">
        <div class="priority-badge" style="--priority-color: {getPriorityColor(task.priority)}">
          <div class="priority-indicator"></div>
          {getPriorityLabel(task.priority)} Priority
        </div>
        
        {#if category}
          <div class="category-badge">
            <div class="category-color" style="background: {category.color}"></div>
            {category.name}
          </div>
        {/if}
      </div>
    </div>

    <div class="task-info">
      <div class="info-item">
        <span class="info-label">Due Date:</span>
        <span class="info-value" class:overdue={task.dueDate && new Date(task.dueDate) < new Date() && !task.completed}>
          {formatDate(task.dueDate)}
        </span>
      </div>

      <div class="info-item">
        <span class="info-label">Created:</span>
        <span class="info-value">{new Date(task.createdAt).toLocaleDateString()}</span>
      </div>

      {#if task.completed && task.completedAt}
        <div class="info-item">
          <span class="info-label">Completed:</span>
          <span class="info-value">{new Date(task.completedAt).toLocaleDateString()}</span>
        </div>
      {/if}
    </div>

    {#if task.notes}
      <div class="task-notes">
        <h3>Notes</h3>
        <p>{task.notes}</p>
      </div>
    {/if}

    {#if task.subtasks && task.subtasks.length > 0}
      <div class="task-subtasks">
        <h3>Subtasks ({task.subtasks.length})</h3>
        <ul class="subtasks-list">
          {#each task.subtasks as subtask}
            <li class="subtask-item">
              <span class="subtask-bullet">•</span>
              {subtask}
            </li>
          {/each}
        </ul>
      </div>
    {/if}

    {#if task.recurrenceRule}
      <div class="task-recurrence">
        <h3>Recurrence</h3>
        <p>This task repeats {task.recurrenceRule.type}ly</p>
      </div>
    {/if}
  </div>
</div>

<style>
  .page-header {
    margin-bottom: 2rem;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
  }

  .breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
  }

  .breadcrumb a {
    color: #4299e1;
    text-decoration: none;
  }

  .breadcrumb a:hover {
    text-decoration: underline;
  }

  .breadcrumb .current {
    color: #1f2937;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    gap: 0.75rem;
  }

  .btn-secondary, .btn-danger {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    font-size: 0.875rem;
    text-decoration: none;
    display: inline-block;
  }

  .btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover {
    background: #e5e7eb;
  }

  .btn-danger {
    background: #dc2626;
    color: white;
  }

  .btn-danger:hover {
    background: #b91c1c;
  }

  .task-detail-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .task-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(226, 232, 240, 0.3);
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  }

  .task-header {
    margin-bottom: 2rem;
  }

  .task-status {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .task-check {
    width: 24px;
    height: 24px;
    border: 2px solid #cbd5e0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    margin-top: 0.25rem;
  }

  .task-check:hover {
    border-color: #4299e1;
  }

  .task-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1a202c;
    margin: 0;
    line-height: 1.2;
  }

  .task-title.completed {
    text-decoration: line-through;
    color: #718096;
  }

  .task-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .priority-badge, .category-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .priority-badge {
    background: rgba(66, 153, 225, 0.1);
    color: var(--priority-color);
    border: 1px solid rgba(66, 153, 225, 0.2);
  }

  .priority-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--priority-color);
  }

  .category-badge {
    background: #f8fafc;
    color: #374151;
    border: 1px solid #e2e8f0;
  }

  .category-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .task-info {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
  }

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .info-label {
    font-weight: 600;
    color: #374151;
  }

  .info-value {
    color: #6b7280;
  }

  .info-value.overdue {
    color: #dc2626;
    font-weight: 600;
  }

  .task-notes, .task-subtasks, .task-recurrence {
    margin-bottom: 2rem;
  }

  .task-notes h3, .task-subtasks h3, .task-recurrence h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
  }

  .task-notes p {
    color: #374151;
    line-height: 1.6;
    white-space: pre-wrap;
  }

  .subtasks-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .subtask-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.5rem 0;
    color: #374151;
  }

  .subtask-bullet {
    color: #4299e1;
    font-weight: bold;
    margin-top: 0.125rem;
  }

  @media (max-width: 768px) {
    .header-content {
      flex-direction: column;
      align-items: stretch;
    }

    .header-actions {
      justify-content: flex-end;
    }

    .task-card {
      padding: 1.5rem;
      margin: 1rem;
    }

    .task-title {
      font-size: 1.5rem;
    }

    .task-meta {
      flex-direction: column;
      align-items: flex-start;
    }

    .info-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.25rem;
    }
  }
</style>
