<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
    <title>Routine Mail - Minimal Dashboard</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont,
          sans-serif;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        color: #1a202c;
        line-height: 1.6;
      }

      .header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(226, 232, 240, 0.8);
        padding: 1.5rem 0;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      }

      .header-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        font-size: 1.75rem;
        font-weight: 800;
        color: #2d3748;
        letter-spacing: -0.025em;
      }

      .logo::after {
        content: "";
        display: inline-block;
        width: 8px;
        height: 8px;
        background: linear-gradient(135deg, #4299e1, #63b3ed);
        border-radius: 50%;
        margin-left: 0.5rem;
        vertical-align: middle;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.7;
        }
      }

      .user-section {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .notification-badge {
        position: relative;
        padding: 0.5rem;
        border-radius: 8px;
        background: #f7fafc;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .notification-badge:hover {
        background: #edf2f7;
      }

      .notification-badge::after {
        content: "3";
        position: absolute;
        top: -4px;
        right: -4px;
        background: #e53e3e;
        color: white;
        font-size: 0.75rem;
        font-weight: 600;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 3rem 2rem;
      }

      .page-header {
        margin-bottom: 3rem;
      }

      .page-title {
        font-size: 2.25rem;
        font-weight: 700;
        color: #1a202c;
        margin-bottom: 0.5rem;
        letter-spacing: -0.025em;
      }

      .page-subtitle {
        color: #718096;
        font-size: 1.125rem;
      }

      .metrics-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 3rem;
      }

      .metric-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        padding: 1.5rem;
        border-radius: 16px;
        border: 1px solid rgba(226, 232, 240, 0.6);
        position: relative;
        transition: all 0.3s ease;
        overflow: hidden;
      }

      .metric-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #4299e1, #63b3ed);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .metric-card:hover {
        border-color: rgba(66, 153, 225, 0.3);
        box-shadow: 0 8px 25px rgba(66, 153, 225, 0.15);
        transform: translateY(-2px);
      }

      .metric-card:hover::before {
        opacity: 1;
      }

      .metric-value {
        font-size: 2rem;
        font-weight: 700;
        color: #1a202c;
        margin-bottom: 0.25rem;
      }

      .metric-label {
        color: #718096;
        font-size: 0.875rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .metric-trend {
        position: absolute;
        top: 1rem;
        right: 1rem;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-weight: 600;
      }

      .trend-up {
        background: #f0fff4;
        color: #38a169;
      }

      .trend-down {
        background: #fed7d7;
        color: #e53e3e;
      }

      .content-grid {
        display: grid;
        grid-template-columns: 1fr 320px;
        gap: 2rem;
      }

      .main-panel {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        border: 1px solid rgba(226, 232, 240, 0.6);
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      }

      .panel-header {
        padding: 1.5rem 2rem;
        border-bottom: 1px solid #e2e8f0;
        background: #f8f9fa;
      }

      .panel-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1a202c;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #4299e1;
      }

      .task-list {
        padding: 0;
      }

      .task-row {
        padding: 1.5rem 2rem;
        border-bottom: 1px solid #f7fafc;
        display: flex;
        align-items: center;
        gap: 1rem;
        transition: background-color 0.15s;
      }

      .task-row:hover {
        background: rgba(66, 153, 225, 0.05);
      }

      .task-row:last-child {
        border-bottom: none;
      }

      .task-check {
        width: 18px;
        height: 18px;
        border: 2px solid #cbd5e0;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
      }

      .task-check:hover {
        border-color: #4299e1;
      }

      .task-details {
        flex: 1;
      }

      .task-name {
        font-weight: 500;
        color: #1a202c;
        margin-bottom: 0.25rem;
      }

      .task-info {
        font-size: 0.875rem;
        color: #718096;
      }

      .task-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .badge-urgent {
        background: #fed7d7;
        color: #c53030;
      }

      .badge-normal {
        background: #e6fffa;
        color: #319795;
      }

      .badge-low {
        background: #ebf8ff;
        color: #3182ce;
      }

      .side-panel {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
      }

      .widget {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        border: 1px solid rgba(226, 232, 240, 0.6);
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      }

      .widget-title {
        font-size: 1rem;
        font-weight: 600;
        color: #1a202c;
        margin-bottom: 1rem;
      }

      .action-btn {
        display: block;
        width: 100%;
        padding: 0.875rem;
        background: linear-gradient(135deg, #4299e1, #3182ce);
        color: white;
        border: none;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 0.75rem;
        text-decoration: none;
        text-align: center;
        box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
      }

      .action-btn:hover {
        background: linear-gradient(135deg, #3182ce, #2c5aa0);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
      }

      .action-btn.secondary {
        background: rgba(247, 250, 252, 0.8);
        color: #4a5568;
        border: 1px solid rgba(226, 232, 240, 0.8);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }

      .action-btn.secondary:hover {
        background: rgba(237, 242, 247, 0.9);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .upcoming-list {
        list-style: none;
      }

      .upcoming-item {
        padding: 0.75rem 0;
        border-bottom: 1px solid #f7fafc;
      }

      .upcoming-item:last-child {
        border-bottom: none;
      }

      .upcoming-date {
        font-size: 0.75rem;
        color: #718096;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 0.25rem;
      }

      .upcoming-task {
        font-weight: 500;
        color: #1a202c;
      }

      @media (max-width: 768px) {
        .content-grid {
          grid-template-columns: 1fr;
        }

        .metrics-row {
          grid-template-columns: repeat(2, 1fr);
        }

        .page-title {
          font-size: 1.875rem;
        }
      }
    </style>
  </head>
  <body>
    <header class="header">
      <div class="header-content">
        <div class="logo">Routine Mail</div>
        <div class="user-section">
          <div class="notification-badge">🔔</div>
          <span style="color: #718096; font-weight: 500"
            ><EMAIL></span
          >
        </div>
      </div>
    </header>

    <div class="container">
      <div class="page-header">
        <h1 class="page-title">Dashboard</h1>
        <p class="page-subtitle">Monday, March 18, 2024</p>
      </div>

      <div class="metrics-row">
        <div class="metric-card">
          <div class="metric-trend trend-up">+2</div>
          <div class="metric-value">12</div>
          <div class="metric-label">Today</div>
        </div>
        <div class="metric-card">
          <div class="metric-trend trend-down">-1</div>
          <div class="metric-value">3</div>
          <div class="metric-label">Overdue</div>
        </div>
        <div class="metric-card">
          <div class="metric-trend trend-up">+5</div>
          <div class="metric-value">8</div>
          <div class="metric-label">This Week</div>
        </div>
        <div class="metric-card">
          <div class="metric-trend trend-up">+3%</div>
          <div class="metric-value">95%</div>
          <div class="metric-label">Completed</div>
        </div>
      </div>

      <div class="content-grid">
        <div class="main-panel">
          <div class="panel-header">
            <h2 class="panel-title">
              <span class="status-dot"></span>
              Active Tasks
            </h2>
          </div>
          <div class="task-list">
            <div class="task-row">
              <input
                type="checkbox"
                class="task-check"
              />
              <div class="task-details">
                <div class="task-name">Review quarterly budget report</div>
                <div class="task-info">Due today at 5:00 PM • Finance</div>
              </div>
              <div class="task-badge badge-urgent">Urgent</div>
            </div>
            <div class="task-row">
              <input
                type="checkbox"
                class="task-check"
              />
              <div class="task-details">
                <div class="task-name">Team standup meeting</div>
                <div class="task-info">Due today at 10:00 AM • Recurring</div>
              </div>
              <div class="task-badge badge-normal">Normal</div>
            </div>
            <div class="task-row">
              <input
                type="checkbox"
                class="task-check"
                checked
              />
              <div class="task-details">
                <div class="task-name">Send project update to stakeholders</div>
                <div class="task-info">Completed at 9:30 AM</div>
              </div>
              <div class="task-badge badge-low">Low</div>
            </div>
          </div>
        </div>

        <div class="side-panel">
          <div class="widget">
            <h3 class="widget-title">Actions</h3>
            <button class="action-btn">+ New Task</button>
            <button class="action-btn secondary">Email Settings</button>
            <button class="action-btn secondary">View Calendar</button>
          </div>

          <div class="widget">
            <h3 class="widget-title">Upcoming</h3>
            <ul class="upcoming-list">
              <li class="upcoming-item">
                <div class="upcoming-date">Tomorrow</div>
                <div class="upcoming-task">Monthly report submission</div>
              </li>
              <li class="upcoming-item">
                <div class="upcoming-date">Friday</div>
                <div class="upcoming-task">Client presentation</div>
              </li>
              <li class="upcoming-item">
                <div class="upcoming-date">Next Monday</div>
                <div class="upcoming-task">Project kickoff meeting</div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
