# OAuth2 流程代码实战解析

## 🎯 核心问题直接回答

### ❓ JWT 是谁的？

**答案：两个都有！**

```javascript
// 1. Office Flow的JWT (存在Routine Mail数据库里)
const officeFlowToken = "eyJhbGciOiJSUzI1NiIs..."; // 用来调用Office Flow API

// 2. Routine Mail的JWT (存在用户浏览器cookie里)
const routineMailToken = "eyJhbGciOiJIUzI1NiIs..."; // 用来在Routine Mail保持登录
```

### ❓ 邮箱冲突怎么办？

**答案：不会冲突，因为用的是 Office Flow 用户 ID，不是邮箱！**

```javascript
// 即使邮箱相同，也是不同的用户
const localUser = { id: "local-123", email: "<EMAIL>" };
const officeFlowUser = { id: "office-456", email: "<EMAIL>" };
// 系统通过office_flow_user_id = "office-456" 来区分
```

## 🔄 完整代码流程

### 步骤 1: 用户点击登录按钮

**文件**: `src/routes/login/+page.svelte`

```javascript
// 用户点击这个按钮
<button on:click={handleOfficeFlowLogin}>Sign in with Office Flow</button>;

async function handleOfficeFlowLogin() {
  // 调用重定向函数
  await redirectToOfficeFlowLogin("/dashboard");
}
```

**文件**: `src/lib/client/auth.js`

```javascript
export async function redirectToOfficeFlowLogin(redirectTo = "/dashboard") {
  // 1. 调用后端API生成授权URL
  const response = await fetch("/api/auth/oauth2/authorize", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ redirectTo, usePopup: false }),
  });

  const data = await response.json();

  // 2. 重定向到Office Flow
  window.location.href = data.authorizationUrl;
  // 例如: http://localhost:8092/api/v1/oauth2/pre-authorize?client_id=routine-mail&...
}
```

### 步骤 2: 生成授权 URL

**文件**: `src/routes/api/auth/oauth2/authorize/+server.ts`

```javascript
export const POST = async ({ request }) => {
  const { redirectTo, usePopup } = await request.json();

  // 1. 生成随机参数
  const state = crypto.randomBytes(32).toString("hex");
  const codeVerifier = crypto.randomBytes(32).toString("base64url");
  const codeChallenge = crypto
    .createHash("sha256")
    .update(codeVerifier)
    .digest("base64url");

  // 2. 存储state数据到文件
  const stateData = {
    state,
    codeVerifier,
    redirectTo: redirectTo || "/dashboard",
    isLinking: false,
    usePopup: usePopup || false,
    timestamp: Date.now(),
  };

  await storeState(state, stateData);

  // 3. 构建Office Flow授权URL
  const authUrl = new URL("http://localhost:8092/api/v1/oauth2/pre-authorize");
  authUrl.searchParams.set("client_id", "routine-mail");
  authUrl.searchParams.set(
    "redirect_uri",
    "http://localhost:3000/auth/callback"
  );
  authUrl.searchParams.set("response_type", "code");
  authUrl.searchParams.set("scope", "openid profile email read");
  authUrl.searchParams.set("state", state);
  authUrl.searchParams.set("code_challenge", codeChallenge);
  authUrl.searchParams.set("code_challenge_method", "S256");

  return json({ authorizationUrl: authUrl.toString() });
};
```

### 步骤 3: Office Flow 处理预认证

**文件**: `OAuth2PreAuthController.java`

```java
@GetMapping("/pre-authorize")
public void preAuthorize(HttpServletRequest request, HttpServletResponse response) {
    // 1. 检查用户是否已登录Office Flow
    Authentication auth = SecurityContextHolder.getContext().getAuthentication();

    if (auth != null && auth.isAuthenticated()) {
        // 用户已登录，直接跳转到授权页面
        String redirectUrl = "http://localhost:3001/oauth2/authorize?" +
                            request.getQueryString();
        response.sendRedirect(redirectUrl);
    } else {
        // 用户未登录，跳转到登录页面
        String redirectUrl = "http://localhost:3001/login?oauth2_redirect=true&" +
                            request.getQueryString();
        response.sendRedirect(redirectUrl);
    }
}
```

### 步骤 4: 用户在 Office Flow 授权

**文件**: `office-flow-frontend/pages/oauth2/authorize.vue`

```javascript
// 用户点击"Allow Access"按钮
async function approve() {
  const response = await $fetch("/api/v1/oauth2/authorize", {
    method: "POST",
    credentials: "include", // 发送Office Flow的登录cookie
    body: JSON.stringify({
      clientId: "routine-mail",
      redirectUri: "http://localhost:3000/auth/callback",
      scope: "openid profile email read",
      state: "abc123...",
      approved: true,
    }),
  });

  // Office Flow返回重定向URL
  // { "success": true, "data": { "redirectUrl": "http://localhost:3000/auth/callback?code=def456&state=abc123" } }

  // 重定向回Routine Mail
  window.location.href = response.data.redirectUrl;
}
```

### 步骤 5: Routine Mail 处理回调

**文件**: `src/routes/api/auth/oauth2/callback/+server.ts`

```javascript
export const GET = async ({ url, cookies }) => {
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");

  // 1. 验证state参数
  const stateData = await consumeState(state);
  if (!stateData) {
    throw new Error("Invalid state");
  }

  // 2. 用授权码交换access token
  const tokenResponse = await fetch(
    "http://localhost:8092/api/v1/oauth2/token",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${Buffer.from(
          "routine-mail:routine-mail-secret"
        ).toString("base64")}`,
      },
      body: new URLSearchParams({
        grant_type: "authorization_code",
        code: code,
        redirect_uri: "http://localhost:3000/auth/callback",
        code_verifier: stateData.codeVerifier,
      }),
    }
  );

  const tokens = await tokenResponse.json();
  // tokens = {
  //   "access_token": "eyJhbGciOiJSUzI1NiIs...",  // 这是Office Flow的JWT
  //   "token_type": "Bearer",
  //   "expires_in": 1800,
  //   "refresh_token": "eyJhbGciOiJSUzI1NiIs...",
  //   "scope": "openid profile email read"
  // }

  // 3. 用Office Flow的access token获取用户信息
  const userResponse = await fetch("http://localhost:8092/api/v1/userinfo", {
    headers: {
      Authorization: `Bearer ${tokens.access_token}`, // 使用Office Flow的JWT
    },
  });

  const officeFlowUser = await userResponse.json();
  // officeFlowUser = {
  //   "sub": "f202f719-8b71-40f7-9eae-6013391ccd3f",  // Office Flow用户ID
  //   "name": "System Administrator",
  //   "email": "<EMAIL>"
  // }

  // 4. 处理用户账户 - 关键部分！
  let user = await getUserByOfficeFlowUserId(officeFlowUser.sub); // 用Office Flow ID查找，不是邮箱！

  if (!user) {
    // 创建新的Routine Mail用户
    user = await createUser({
      email: officeFlowUser.email,
      password: "", // OAuth2用户没有密码
      name: officeFlowUser.name,
      isVerified: true,
    });

    // 创建Office Flow链接
    await createOfficeFlowLink({
      userId: user.id,
      officeFlowUserId: officeFlowUser.sub, // 存储Office Flow用户ID
      accessToken: tokens.access_token, // 存储Office Flow的JWT
      refreshToken: tokens.refresh_token,
      expiresAt: new Date(Date.now() + tokens.expires_in * 1000),
    });
  }

  // 5. 生成Routine Mail的JWT
  const routineMailJWT = generateJWT({
    userId: user.id,
    email: user.email,
  });

  // 6. 设置Routine Mail的JWT到cookie
  cookies.set("auth-token", routineMailJWT, {
    path: "/",
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
    maxAge: 7 * 24 * 60 * 60, // 7天
  });

  // 7. 重定向到dashboard
  return redirect(302, "/dashboard");
};
```

## 🗄️ 数据库中的实际数据

### users 表

```sql
-- 本地注册的用户
INSERT INTO users VALUES (
  'local-user-123',
  '<EMAIL>',
  'Local User',
  '$2a$12$hashedPassword...', -- 有密码
  true
);

-- OAuth2用户 (同样的邮箱，但不同的用户)
INSERT INTO users VALUES (
  'oauth-user-456',
  '<EMAIL>',
  'Office Flow User',
  '', -- 没有密码
  true
);
```

### office_flow_links 表

```sql
-- 只有OAuth2用户才有这个记录
INSERT INTO office_flow_links VALUES (
  'link-789',
  'oauth-user-456', -- Routine Mail用户ID
  'f202f719-8b71-40f7-9eae-6013391ccd3f', -- Office Flow用户ID (关键!)
  'eyJhbGciOiJSUzI1NiIs...', -- Office Flow的access token
  'eyJhbGciOiJSUzI1NiIs...', -- Office Flow的refresh token
  '2024-01-01 12:30:00'
);
```

### sessions 表

```sql
-- 两个用户都可以有session
INSERT INTO sessions VALUES (
  'session-111',
  'local-user-123', -- 本地用户的session
  'eyJhbGciOiJIUzI1NiIs...', -- Routine Mail的JWT
  '2024-01-08 12:00:00'
);

INSERT INTO sessions VALUES (
  'session-222',
  'oauth-user-456', -- OAuth2用户的session
  'eyJhbGciOiJIUzI1NiIs...', -- Routine Mail的JWT
  '2024-01-08 12:00:00'
);
```

## 🔍 邮箱冲突的具体处理

### 当前代码逻辑

```javascript
// src/lib/server/db/operations.js
export async function getUserByOfficeFlowUserId(officeFlowUserId) {
  // 关键：用Office Flow用户ID查找，不是邮箱！
  const link = await db.office_flow_links.findFirst({
    where: { office_flow_user_id: officeFlowUserId },
    include: { user: true },
  });

  return link?.user || null;
}

// 即使邮箱相同，也会创建新用户
export async function createUser(userData) {
  return await db.users.create({
    data: {
      id: crypto.randomUUID(),
      email: userData.email, // 允许重复邮箱
      name: userData.name,
      password: userData.password,
      isVerified: userData.isVerified,
    },
  });
}
```

### 如果要强制邮箱唯一，可以这样改：

```javascript
export async function handleOfficeFlowUser(officeFlowUser, tokens) {
  // 先检查邮箱是否已存在
  const existingUser = await getUserByEmail(officeFlowUser.email);

  if (existingUser) {
    // 选项1: 链接到现有账户
    await createOfficeFlowLink({
      userId: existingUser.id,
      officeFlowUserId: officeFlowUser.sub,
      accessToken: tokens.access_token,
      refreshToken: tokens.refresh_token,
    });
    return existingUser;

    // 选项2: 抛出错误
    // throw new Error('Email already exists');

    // 选项3: 修改邮箱
    // officeFlowUser.email = `${officeFlowUser.email}.oauth`;
  }

  // 创建新用户...
}
```

## 🎯 总结

1. **两个 JWT**：Office Flow 的存数据库，Routine Mail 的存 cookie
2. **邮箱可重复**：用 Office Flow 用户 ID 区分，不是邮箱
3. **独立账户**：OAuth2 用户和本地用户完全独立
4. **安全机制**：PKCE + State + JWT 过期时间

## 🧪 实际测试场景

### 场景 1: 邮箱冲突测试

```javascript
// 1. 本地用户已存在
const localUser = await createUser({
  email: "<EMAIL>",
  password: "password123",
  name: "Local User",
});
console.log("本地用户:", localUser.id); // local-user-123

// 2. Office Flow也有同样邮箱的用户登录
const officeFlowUser = {
  sub: "office-flow-456",
  email: "<EMAIL>", // 同样的邮箱！
  name: "Office Flow User",
};

// 3. 系统会创建新的用户，不会冲突
const oauthUser = await handleOAuth2Login(officeFlowUser);
console.log("OAuth2用户:", oauthUser.id); // oauth-user-789

// 4. 数据库中的结果
/*
users表:
- local-user-123, <EMAIL>, Local User, $2a$12$hash...
- oauth-user-789, <EMAIL>, Office Flow User, (空密码)

office_flow_links表:
- oauth-user-789 <-> office-flow-456
*/
```

### 场景 2: JWT Token 的实际内容

```javascript
// Office Flow返回的JWT (RS256签名)
const officeFlowJWT = {
  header: {
    alg: "RS256",
    typ: "JWT",
    kid: "office-flow-key-1",
  },
  payload: {
    sub: "f202f719-8b71-40f7-9eae-6013391ccd3f", // Office Flow用户ID
    aud: ["routine-mail"],
    iss: "http://localhost:8092/api/v1",
    exp: 1640995200,
    iat: 1640991600,
    scope: "openid profile email read",
    email: "<EMAIL>",
    name: "System Administrator",
  },
};

// Routine Mail生成的JWT (HS256签名)
const routineMailJWT = {
  header: {
    alg: "HS256",
    typ: "JWT",
  },
  payload: {
    userId: "oauth-user-789", // Routine Mail用户ID
    email: "<EMAIL>",
    iat: 1640991600,
    exp: 1641595200, // 7天后过期
  },
};
```

### 场景 3: 用户登录后的状态

```javascript
// 用户登录后，浏览器cookie中有：
document.cookie;
// "auth-token=eyJhbGciOiJIUzI1NiIs...; HttpOnly; Secure; SameSite=Strict"

// 数据库中存储的Office Flow token：
const userTokens = await getOfficeFlowTokens(userId);
console.log(userTokens);
/*
{
  accessToken: "eyJhbGciOiJSUzI1NiIs...",  // 用来调用Office Flow API
  refreshToken: "eyJhbGciOiJSUzI1NiIs...", // 用来刷新access token
  expiresAt: "2024-01-01T13:00:00Z"
}
*/
```

## 🔧 调试和验证代码

### 验证 JWT 内容

```javascript
// 解码JWT查看内容 (不验证签名)
function decodeJWT(token) {
  const parts = token.split(".");
  const payload = JSON.parse(atob(parts[1]));
  return payload;
}

// 测试Office Flow JWT
const officeFlowToken = "eyJhbGciOiJSUzI1NiIs...";
console.log("Office Flow JWT:", decodeJWT(officeFlowToken));

// 测试Routine Mail JWT
const routineMailToken = "eyJhbGciOiJIUzI1NiIs...";
console.log("Routine Mail JWT:", decodeJWT(routineMailToken));
```

### 检查用户链接状态

```javascript
// src/lib/server/db/operations.js
export async function checkUserLinkStatus(email) {
  // 1. 查找所有同邮箱用户
  const users = await db.users.findMany({
    where: { email },
    include: { officeFlowLink: true },
  });

  console.log(
    `邮箱 ${email} 的用户:`,
    users.map((u) => ({
      id: u.id,
      name: u.name,
      hasPassword: !!u.password,
      hasOfficeFlowLink: !!u.officeFlowLink,
      officeFlowUserId: u.officeFlowLink?.office_flow_user_id,
    }))
  );
}

// 测试
await checkUserLinkStatus("<EMAIL>");
/*
输出:
邮箱 <EMAIL> 的用户: [
  {
    id: "local-user-123",
    name: "Local User",
    hasPassword: true,
    hasOfficeFlowLink: false,
    officeFlowUserId: null
  },
  {
    id: "oauth-user-789",
    name: "Office Flow User",
    hasPassword: false,
    hasOfficeFlowLink: true,
    officeFlowUserId: "f202f719-8b71-40f7-9eae-6013391ccd3f"
  }
]
*/
```

### API 调用示例

```javascript
// 在Routine Mail中调用Office Flow API
export async function callOfficeFlowAPI(userId, endpoint) {
  // 1. 获取存储的Office Flow token
  const link = await getOfficeFlowLink(userId);
  if (!link) {
    throw new Error("User not linked to Office Flow");
  }

  // 2. 检查token是否过期
  if (link.expiresAt < new Date()) {
    // 刷新token
    await refreshOfficeFlowToken(link);
  }

  // 3. 调用Office Flow API
  const response = await fetch(`http://localhost:8092/api/v1${endpoint}`, {
    headers: {
      Authorization: `Bearer ${link.accessToken}`, // 使用Office Flow的JWT
    },
  });

  return response.json();
}

// 使用示例
const userProfile = await callOfficeFlowAPI(userId, "/users/profile");
const userPermissions = await callOfficeFlowAPI(userId, "/users/permissions");
```

## 🚨 常见问题和解决方案

### 问题 1: "为什么有两个 JWT？"

```javascript
// 答案：不同的用途！

// Office Flow JWT - 用来访问Office Flow的资源
fetch("http://localhost:8092/api/v1/users/profile", {
  headers: { Authorization: `Bearer ${officeFlowJWT}` },
});

// Routine Mail JWT - 用来在Routine Mail保持登录状态
fetch("/api/dashboard/data", {
  // 自动发送cookie中的Routine Mail JWT
  credentials: "include",
});
```

### 问题 2: "邮箱重复怎么办？"

```javascript
// 当前策略：允许重复，用Office Flow ID区分
const findUser = async (officeFlowUserId) => {
  // 不是用邮箱查找！
  return await getUserByOfficeFlowUserId(officeFlowUserId);
};

// 如果要强制唯一邮箱：
const handleEmailConflict = async (officeFlowUser) => {
  const existing = await getUserByEmail(officeFlowUser.email);

  if (existing && !existing.officeFlowLink) {
    // 选项1: 合并账户
    await linkExistingUser(existing.id, officeFlowUser.sub);
    return existing;
  }

  if (existing && existing.officeFlowLink) {
    throw new Error("Email already linked to another Office Flow account");
  }

  // 创建新用户
  return await createUser(officeFlowUser);
};
```

### 问题 3: "如何退出登录？"

```javascript
// 退出Routine Mail
export async function logout(cookies) {
  // 1. 清除cookie
  cookies.delete("auth-token");

  // 2. 删除session记录
  await deleteSession(sessionToken);

  // 注意：不删除Office Flow tokens，用户可能还想用Office Flow
}

// 完全断开Office Flow链接
export async function unlinkOfficeFlow(userId) {
  // 1. 删除Office Flow链接
  await deleteOfficeFlowLink(userId);

  // 2. 如果用户没有密码，需要设置密码或删除账户
  const user = await getUser(userId);
  if (!user.password) {
    // 提示用户设置密码或删除账户
  }
}
```

## 🎯 最终总结

**简单来说：**

1. **点击登录** → 跳转到 Office Flow
2. **Office Flow 授权** → 返回授权码
3. **交换 token** → 获得 Office Flow 的 JWT
4. **获取用户信息** → 用 Office Flow JWT 调用 userinfo
5. **创建本地用户** → 用 Office Flow 用户 ID 作为唯一标识
6. **生成本地 JWT** → 用于 Routine Mail 的 session
7. **设置 cookie** → 用户在 Routine Mail 保持登录状态

**关键点：**

- 两个 JWT 各有用途，不冲突
- 邮箱可以重复，用 Office Flow 用户 ID 区分
- OAuth2 用户和本地用户完全独立

这样够详细了吗？每个概念都有具体的代码实现！
