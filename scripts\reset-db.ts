import postgres from 'postgres';
import { config } from 'dotenv';

config();

const connectionString = process.env.DATABASE_URL!;

async function resetDatabase() {
  const client = postgres(connectionString, { max: 1 });
  
  try {
    console.log('Dropping existing tables...');
    
    // Drop tables in correct order (considering foreign keys)
    await client`DROP TABLE IF EXISTS sessions CASCADE`;
    await client`DROP TABLE IF EXISTS otp_requests CASCADE`;
    await client`DROP TABLE IF EXISTS email_limits CASCADE`;
    await client`DROP TABLE IF EXISTS users CASCADE`;
    
    console.log('All tables dropped successfully!');
    console.log('You can now run: npm run db:migrate');
    
  } catch (error) {
    console.error('Error resetting database:', error);
  } finally {
    await client.end();
  }
}

resetDatabase();
