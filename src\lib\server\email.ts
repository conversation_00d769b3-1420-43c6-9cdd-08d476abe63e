import nodemailer from 'nodemailer';
import { config } from 'dotenv';

config();

const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
  tls: {
    rejectUnauthorized: false,
  },
});

export interface SendOTPEmailOptions {
  to: string;
  code: string;
  type: 'registration' | 'login';
}

export async function sendOTPEmail({ to, code, type }: SendOTPEmailOptions): Promise<boolean> {
  try {
    const subject = type === 'registration'
      ? 'Routine Mail - Email Verification Code'
      : 'Routine Mail - Login Verification Code';

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .logo { font-size: 24px; font-weight: bold; color: #2d3748; }
            .code-box { background: #f7fafc; border: 2px solid #e2e8f0; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0; }
            .code { font-size: 32px; font-weight: bold; color: #4299e1; letter-spacing: 4px; }
            .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0; font-size: 14px; color: #718096; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">Routine Mail</div>
            </div>
            
            <h2>Email Verification</h2>
            <p>Hello,</p>
            <p>Your verification code for ${type === 'registration' ? 'account registration' : 'login'} is:</p>
            
            <div class="code-box">
              <div class="code">${code}</div>
            </div>
            
            <p><strong>Important:</strong></p>
            <ul>
              <li>This code will expire in 5 minutes</li>
              <li>You have 3 attempts to enter the correct code</li>
              <li>Do not share this code with anyone</li>
            </ul>
            
            <p>If you didn't request this code, please ignore this email.</p>
            
            <div class="footer">
              <p>This email was sent by Routine Mail</p>
              <p>If you have any questions, please contact our support team.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    await transporter.sendMail({
      from: {
        name: 'Routine Mail',
        address: '<EMAIL>'
      },
      to,
      subject,
      html,
    });

    return true;
  } catch (error) {
    console.error('Failed to send OTP email:', error);
    return false;
  }
}

export async function sendEmail(to: string, subject: string, html: string): Promise<void> {
  try {
    await transporter.sendMail({
      from: {
        name: 'Routine Mail',
        address: '<EMAIL>'
      },
      to,
      subject,
      html,
    });

    console.log(`Email sent successfully to ${to}`);
  } catch (error) {
    console.error('Failed to send email:', error);
    throw error;
  }
}
