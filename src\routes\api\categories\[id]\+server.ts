import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { verifyJWT } from '$lib/server/auth.js';
import { getCategoryById, updateCategory, deleteCategory } from '$lib/server/db/operations.js';

export const GET: RequestHandler = async ({ params, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const category = await getCategoryById(params.id, payload.userId);
    if (!category) {
      return json({ error: 'Category not found' }, { status: 404 });
    }

    return json({ category });
  } catch (error) {
    console.error('Get category error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const PUT: RequestHandler = async ({ params, request, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if category exists
    const existingCategory = await getCategoryById(params.id, payload.userId);
    if (!existingCategory) {
      return json({ error: 'Category not found' }, { status: 404 });
    }

    const updates = await request.json();

    // Validate name if provided
    if (updates.name !== undefined && (!updates.name || updates.name.trim() === '')) {
      return json({ error: 'Category name cannot be empty' }, { status: 400 });
    }

    // Validate color format if provided
    if (updates.color && !/^#[0-9A-F]{6}$/i.test(updates.color)) {
      return json({ error: 'Color must be a valid hex color (e.g., #FF0000)' }, { status: 400 });
    }

    // Clean up string fields
    if (updates.name) updates.name = updates.name.trim();

    await updateCategory(params.id, payload.userId, updates);

    // Return updated category
    const updatedCategory = await getCategoryById(params.id, payload.userId);
    return json({ category: updatedCategory });
  } catch (error) {
    console.error('Update category error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const DELETE: RequestHandler = async ({ params, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if category exists
    const existingCategory = await getCategoryById(params.id, payload.userId);
    if (!existingCategory) {
      return json({ error: 'Category not found' }, { status: 404 });
    }

    await deleteCategory(params.id, payload.userId);

    return json({ message: 'Category deleted successfully' });
  } catch (error) {
    console.error('Delete category error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
