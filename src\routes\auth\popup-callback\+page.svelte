<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';

  let status = 'processing';
  let message = 'Processing OAuth2 callback...';

  onMount(async () => {
    try {
      // Get URL parameters
      const code = $page.url.searchParams.get('code');
      const state = $page.url.searchParams.get('state');
      const error = $page.url.searchParams.get('error');

      if (error) {
        status = 'error';
        message = `OAuth2 error: ${error}`;
        
        // Notify parent window of error
        if (window.opener) {
          window.opener.postMessage({ type: 'oauth2-error', error }, '*');
        }
        
        setTimeout(() => window.close(), 3000);
        return;
      }

      if (!code || !state) {
        status = 'error';
        message = 'Missing required OAuth2 parameters';
        
        // Notify parent window of error
        if (window.opener) {
          window.opener.postMessage({ type: 'oauth2-error', error: message }, '*');
        }
        
        setTimeout(() => window.close(), 3000);
        return;
      }

      // Process the OAuth2 callback
      const response = await fetch('/api/auth/oauth2/callback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code, state })
      });

      if (response.ok) {
        status = 'success';
        message = 'Login successful! Closing window...';
        
        // Notify parent window of success
        if (window.opener) {
          window.opener.postMessage({ type: 'oauth2-success' }, '*');
        }
        
        // Close popup after short delay
        setTimeout(() => window.close(), 1500);
      } else {
        const errorData = await response.json();
        status = 'error';
        message = errorData.error || 'Login failed';
        
        // Notify parent window of error
        if (window.opener) {
          window.opener.postMessage({ type: 'oauth2-error', error: message }, '*');
        }
        
        setTimeout(() => window.close(), 3000);
      }

    } catch (err) {
      console.error('OAuth2 callback error:', err);
      status = 'error';
      message = 'An unexpected error occurred';
      
      // Notify parent window of error
      if (window.opener) {
        window.opener.postMessage({ type: 'oauth2-error', error: message }, '*');
      }
      
      setTimeout(() => window.close(), 3000);
    }
  });
</script>

<svelte:head>
  <title>OAuth2 Callback - Routine Mail</title>
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
  <div class="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
    {#if status === 'processing'}
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <h2 class="text-xl font-semibold text-gray-900 mb-2">Processing...</h2>
      <p class="text-gray-600">{message}</p>
    {:else if status === 'success'}
      <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
        <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
      </div>
      <h2 class="text-xl font-semibold text-gray-900 mb-2">Success!</h2>
      <p class="text-gray-600">{message}</p>
    {:else if status === 'error'}
      <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </div>
      <h2 class="text-xl font-semibold text-gray-900 mb-2">Error</h2>
      <p class="text-gray-600">{message}</p>
    {/if}
  </div>
</div>

<style>
  /* Ensure the page looks good in popup */
  :global(body) {
    margin: 0;
    padding: 0;
  }
</style>
