import { json } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from './$types';
import { verifyJWT } from '$lib/server/auth.js';
import { getTaskById, completeTask } from '$lib/server/db/operations.js';

export const POST: RequestHandler = async ({ params, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // Check if task exists
    const existingTask = await getTaskById(params.id, payload.userId);
    if (!existingTask) {
      return json({ error: 'Task not found' }, { status: 404 });
    }

    if (existingTask.completed) {
      return json({ error: 'Task is already completed' }, { status: 400 });
    }

    await completeTask(params.id, payload.userId);

    // Return updated task
    const updatedTask = await getTaskById(params.id, payload.userId);
    return json({ task: updatedTask, message: 'Task completed successfully' });
  } catch (error) {
    console.error('Complete task error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
