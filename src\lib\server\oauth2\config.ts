/**
 * OAuth2 Configuration for Office Flow Integration
 */

export const OAUTH2_CONFIG = {
  // Office Flow OAuth2 endpoints
  OFFICE_FLOW_BASE_URL: 'http://localhost:8092/api/v1',
  AUTHORIZE_URL: 'http://localhost:8092/api/v1/oauth2/pre-authorize', // Office Flow Backend pre-authorization endpoint
  TOKEN_URL: 'http://localhost:8092/api/v1/oauth2/token',
  USERINFO_URL: 'http://localhost:8092/api/v1/userinfo',
  LOGOUT_URL: 'http://localhost:8092/api/v1/oauth2/logout',

  // Client configuration (should match Office Flow backend configuration)
  CLIENT_ID: 'routine-mail',
  CLIENT_SECRET: 'routine-mail-secret', // This should be configured in Office Flow backend

  // Redirect URIs
  REDIRECT_URI: 'http://localhost:3000/auth/callback',
  POPUP_REDIRECT_URI: 'http://localhost:3000/auth/popup-callback',

  // OAuth2 scopes
  SCOPES: ['openid', 'profile', 'email', 'read'],

  // PKCE configuration
  CODE_CHALLENGE_METHOD: 'S256',

  // State parameter configuration
  STATE_LENGTH: 32,

  // Token storage
  ACCESS_TOKEN_EXPIRY: 30 * 60, // 30 minutes
  REFRESH_TOKEN_EXPIRY: 7 * 24 * 60 * 60, // 7 days
} as const;

/**
 * Environment-specific configuration
 */
export function getOAuth2Config() {
  const isDev = process.env.NODE_ENV === 'development';

  return {
    ...OAUTH2_CONFIG,
    OFFICE_FLOW_BASE_URL: process.env.OFFICE_FLOW_BASE_URL || OAUTH2_CONFIG.OFFICE_FLOW_BASE_URL,
    AUTHORIZE_URL: process.env.OFFICE_FLOW_AUTHORIZE_URL || OAUTH2_CONFIG.AUTHORIZE_URL,
    TOKEN_URL: process.env.OFFICE_FLOW_TOKEN_URL || OAUTH2_CONFIG.TOKEN_URL,
    USERINFO_URL: process.env.OFFICE_FLOW_USERINFO_URL || OAUTH2_CONFIG.USERINFO_URL,
    LOGOUT_URL: process.env.OFFICE_FLOW_LOGOUT_URL || OAUTH2_CONFIG.LOGOUT_URL,
    CLIENT_ID: process.env.OFFICE_FLOW_CLIENT_ID || OAUTH2_CONFIG.CLIENT_ID,
    CLIENT_SECRET: process.env.OFFICE_FLOW_CLIENT_SECRET || OAUTH2_CONFIG.CLIENT_SECRET,
    REDIRECT_URI: process.env.OFFICE_FLOW_REDIRECT_URI || OAUTH2_CONFIG.REDIRECT_URI,
  };
}
