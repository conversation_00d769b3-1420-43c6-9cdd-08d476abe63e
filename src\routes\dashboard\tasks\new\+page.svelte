<script lang="ts">
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';
  import RecurrenceEditor from '$lib/client/RecurrenceEditor.svelte';
  import { slide } from 'svelte/transition';

  export let data: PageData;

  let title = '';
  let notes = '';
  let priority = 1;
  let categoryId = '';
  let dueDate = '';
  let dueTime = '';
  let hasDueDate = false;
  let subtasks: string[] = [''];
  let loading = false;
  let error = '';
  let success = '';

  // New recurrence rule state
  let hasRecurrence = false;
  let recurrenceRule: any = null;

  const priorityOptions = [
    { value: 0, label: 'Low', color: '#64748b' },
    { value: 1, label: 'Normal', color: '#059669' },
    { value: 2, label: 'High', color: '#dc2626' }
  ];

  function addSubtask() {
    subtasks = [...subtasks, ''];
  }

  function removeSubtask(index: number) {
    subtasks = subtasks.filter((_, i) => i !== index);
  }

  function handleRecurrenceChange(event: CustomEvent) {
    recurrenceRule = event.detail;
  }

  async function handleSubmit() {
    if (!title.trim()) {
      error = 'Title is required';
      return;
    }

    loading = true;
    error = '';

    try {
      // Combine date and time if both are provided
      let combinedDueDate = null;
      if (hasDueDate && dueDate) {
        if (dueTime) {
          combinedDueDate = new Date(`${dueDate}T${dueTime}`);
        } else {
          combinedDueDate = new Date(`${dueDate}T09:00`); // Default to 9 AM
        }
      }

      // Filter out empty subtasks
      const validSubtasks = subtasks.filter(s => s.trim() !== '');

      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: title.trim(),
          notes: notes.trim() || null,
          priority,
          categoryId: categoryId || null,
          dueDate: combinedDueDate?.toISOString() || null,
          subtasks: validSubtasks,
          recurrenceRule: hasRecurrence ? recurrenceRule : null
        }),
      });

      const result = await response.json();

      if (response.ok) {
        // Show brief success message then redirect
        success = 'Task created successfully!';
        // Small delay to show success message, then redirect
        setTimeout(() => {
          goto('/dashboard', { invalidateAll: true });
        }, 500);
      } else {
        error = result.error || 'Failed to create task';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }
</script>

<svelte:head>
  <title>New Task - Routine Mail</title>
</svelte:head>

<div class="page-header">
  <div class="breadcrumb">
    <a href="/dashboard">Dashboard</a>
    <span>›</span>
    <span class="current">New Task</span>
  </div>
  <h1 class="page-title">Create New Task</h1>
  <p class="page-subtitle">Add a new task to your routine</p>
</div>

<div class="form-container">
  <form on:submit|preventDefault={handleSubmit} class="task-form">
    {#if error}
      <div class="error-message">{error}</div>
    {/if}

    {#if success}
      <div class="success-message">{success}</div>
    {/if}

    <!-- Essential Information Section -->
    <div class="form-section">
      <div class="section-header">
        <h3 class="section-title">Essential Information</h3>
        <p class="section-subtitle">The basics to get your task started</p>
      </div>

      <div class="form-group">
        <label for="title">Task Title *</label>
        <div class="title-input-row">
          <input
            id="title"
            type="text"
            bind:value={title}
            placeholder="What needs to be done?"
            required
            disabled={loading}
            class="primary-input"
          />
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Priority Level</label>
          <div class="priority-selector">
            {#each priorityOptions as option}
              <button
                type="button"
                class="priority-btn"
                class:selected={priority === option.value}
                style="--priority-color: {option.color}"
                on:click={() => priority = option.value}
                disabled={loading}
              >
                <div class="priority-indicator"></div>
                {option.label}
              </button>
            {/each}
          </div>
        </div>
      </div>
      <div class="form-group">
        <div class="due-date-toggle">
          <label class="toggle-label">
            <input
              type="checkbox"
              bind:checked={hasDueDate}
              disabled={loading}
              class="toggle-checkbox"
            />
            <span class="toggle-text">Set Due Date</span>
          </label>
        </div>

        {#if hasDueDate}
          <div class="due-date-inputs" transition:slide>
            <div class="date-input-group">
              <input
                type="date"
                bind:value={dueDate}
                disabled={loading}
                class="date-input"
              />
            </div>
            <div class="time-input-group">
              <input
                type="time"
                bind:value={dueTime}
                disabled={loading}
                class="time-input"
                placeholder="09:00"
              />
            </div>
          </div>
        {/if}
      </div>

      <div class="form-group">
        <label>Subtasks (Optional)</label>
        <div class="subtasks-container">
          {#each subtasks as subtask, index}
            <div class="subtask-row">
              <input
                type="text"
                bind:value={subtasks[index]}
                placeholder="Enter subtask"
                disabled={loading}
              />
              {#if subtasks.length > 1}
                <button
                  type="button"
                  class="remove-subtask-btn"
                  on:click={() => removeSubtask(index)}
                  disabled={loading}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              {/if}
            </div>
          {/each}
          <button
            type="button"
            class="add-subtask-btn"
            on:click={addSubtask}
            disabled={loading}
          >
            + Add Subtask
          </button>
        </div>
      </div>
    </div>

    <!-- Additional Details Section -->
    <div class="form-section collapsible">
      <div class="section-header">
        <h3 class="section-title">Additional Details</h3>
        <p class="section-subtitle">Optional information to organize your task</p>
      </div>

      <div class="form-group">
        <label>Category</label>
        <div class="category-selector">
          <button
            type="button"
            class="category-btn"
            class:selected={!categoryId}
            on:click={() => categoryId = ''}
            disabled={loading}
          >
            <div class="category-color" style="background: #e5e7eb;"></div>
            No category
          </button>
          {#each data.categories as category}
            <button
              type="button"
              class="category-btn"
              class:selected={categoryId === category.id}
              on:click={() => categoryId = category.id}
              disabled={loading}
            >
              <div class="category-color" style="background: {category.color};"></div>
              {category.name}
            </button>
          {/each}
        </div>
      </div>



      <div class="form-group">
        <label for="notes">Notes</label>
        <textarea
          id="notes"
          bind:value={notes}
          placeholder="Add any additional notes or details..."
          rows="3"
          disabled={loading}
        ></textarea>
      </div>
    </div>



    <!-- Advanced Settings Section -->
    <div class="form-section advanced">
      <div class="section-header">
        <h3 class="section-title">Advanced Settings</h3>
        <p class="section-subtitle">Set up recurring tasks and automation</p>
      </div>

      <div class="form-group">
        <div class="recurrence-toggle">
          <label class="toggle-label">
            <input
              type="checkbox"
              bind:checked={hasRecurrence}
              disabled={loading}
              class="toggle-checkbox"
            />
            <span class="toggle-text">Make this a recurring task</span>
          </label>
        </div>

        {#if hasRecurrence}
          <div transition:slide>
            <RecurrenceEditor rule={recurrenceRule} on:change={handleRecurrenceChange} />
          </div>
        {/if}
      </div>
    </div>

    <div class="form-actions">
      <button type="button" class="btn-secondary" on:click={() => goto('/dashboard')} disabled={loading}>
        Cancel
      </button>
      <button type="submit" class="btn-primary" disabled={loading}>
        {#if loading}
          <span class="loading-spinner"></span>
          Creating...
        {:else}
          Create Task
        {/if}
      </button>
    </div>
  </form>
</div>

<style>
  .page-header {
    margin-bottom: 1rem;
  }

  .breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
  }

  .breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
  }

  .breadcrumb a:hover {
    text-decoration: underline;
  }

  .breadcrumb .current {
    color: #1f2937;
    font-weight: 500;
  }

  .page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.25rem;
    letter-spacing: -0.02em;
  }

  .page-subtitle {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
    font-weight: 500;
  }

  .form-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .task-form {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    border: 1px solid rgba(226, 232, 240, 0.3);
    padding: 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    overflow: hidden;
  }

  /* Form sections */
  .form-section {
    padding: 1.5rem;
    border-bottom: 1px solid #f1f5f9;
  }

  .form-section:last-of-type {
    border-bottom: none;
  }

  .form-section.collapsible {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  }

  .form-section.advanced {
    background: linear-gradient(135deg, #fef7ff, #f3e8ff);
    border-top: 2px solid #e879f9;
  }

  .section-header {
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 1rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.125rem;
  }

  .section-subtitle {
    color: #6b7280;
    font-size: 0.75rem;
    margin: 0;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  input, textarea, select {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.2s ease;
  }

  .title-input-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .title-input-row .primary-input {
    flex: 1;
    border: 2px solid #d1d5db !important;
    font-size: 1rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.95);
    padding: 0.75rem;
    border-radius: 10px;
  }

  .primary-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.08);
  }

  /* Toggle styles */
  .toggle-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .toggle-checkbox {
    width: 20px;
    height: 20px;
    margin: 0;
    accent-color: #3b82f6;
  }

  .toggle-text {
    color: #374151;
  }

  /* Due date inputs */
  .due-date-inputs {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 0.75rem;
    margin-top: 0.75rem;
    padding: 1rem;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
  }

  .date-input, .time-input {
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid #d1d5db;
    font-size: 0.875rem;
    background: white;
  }

  input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #3b82f6;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
  }

  /* Recurrence styles */
  .recurrence-config {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16px;
    padding: 1.5rem;
    margin-top: 1rem;
    border: 1px solid #e5e7eb;
  }

  .quick-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.75rem;
    margin-top: 0.75rem;
  }

  .quick-btn {
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    background: white;
    color: #374151;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    text-align: center;
    font-size: 0.875rem;
  }

  .quick-btn:hover:not(:disabled) {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
  }

  .quick-btn.selected {
    border-color: #3b82f6;
    background: #3b82f6;
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .custom-inputs {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.75rem;
    flex-wrap: wrap;
  }

  .interval-input {
    width: 80px;
    padding: 0.5rem;
    text-align: center;
    font-weight: 600;
  }

  .type-select {
    min-width: 120px;
    padding: 0.5rem;
  }

  .small-number-input {
    width: 60px;
    padding: 0.25rem 0.5rem;
    margin: 0 0.5rem;
    text-align: center;
    font-size: 0.875rem;
  }

  .end-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 0.75rem;
  }

  .end-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
    cursor: pointer;
  }

  .end-option input[type="radio"] {
    width: auto;
    margin: 0;
  }

  input:disabled, textarea:disabled, select:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
  }

  /* Form actions */
  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
    border-top: 1px solid #e9ecef;
  }

  .btn-primary, .btn-secondary {
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  }

  .btn-secondary {
    background: rgba(255, 255, 255, 0.8);
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover:not(:disabled) {
    background: rgba(249, 250, 251, 0.9);
    transform: translateY(-1px);
  }

  .btn-primary:disabled, .btn-secondary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  textarea {
    resize: vertical;
    min-height: 80px;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .form-container {
      margin: 1rem;
    }

    .task-form {
      border-radius: 20px;
    }

    .form-section {
      padding: 1.5rem;
    }

    .page-title {
      font-size: 2rem;
    }

    .form-row {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .due-date-inputs {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .quick-buttons {
      grid-template-columns: repeat(2, 1fr);
      gap: 0.5rem;
    }

    .custom-inputs {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;
    }

    .interval-input, .type-select {
      width: 100%;
    }

    .form-actions {
      flex-direction: column;
      gap: 1rem;
      padding: 1.5rem;
    }

    .btn-primary, .btn-secondary {
      width: 100%;
      justify-content: center;
    }
  }

  /* Message styles */
  .error-message {
    background: linear-gradient(135deg, #fef2f2, #fed7d7);
    color: #dc2626;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 600;
    border: 1px solid #fecaca;
  }

  .success-message {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    color: #16a34a;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 600;
    border: 1px solid #bbf7d0;
  }

  /* Priority selector styles */
  .priority-selector {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }

  .priority-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    background: white;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 600;
    color: #374151;
    text-align: center;
  }

  .priority-btn:hover:not(:disabled):not(.selected) {
    border-color: var(--priority-color);
    background: rgba(59, 130, 246, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .priority-btn.selected {
    border-color: var(--priority-color);
    background: var(--priority-color);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .priority-btn.selected:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
    /* Keep the same background color, just enhance the shadow */
  }

  .priority-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--priority-color);
  }

  /* When selected, make the indicator white to contrast with colored background */
  .priority-btn.selected .priority-indicator {
    background: white;
  }

  /* When not selected, show the priority color */
  .priority-btn:not(.selected) .priority-indicator {
    background: var(--priority-color);
  }

  /* Category selector styles */
  .category-selector {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-height: 200px;
    overflow-y: auto;
  }

  .category-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    font-weight: 500;
    color: #374151;
  }

  .category-btn:hover:not(:disabled) {
    border-color: #d1d5db;
    background: #f9fafb;
  }

  .category-btn.selected {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
  }

  .category-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  /* Subtasks styles */



  /* Subtasks styles */
  .subtasks-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .subtask-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .subtask-row input {
    flex: 1;
    padding: 0.75rem;
    border-radius: 10px;
  }

  .remove-subtask-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #e5e7eb;
    background: white;
    color: #6b7280;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
  }

  .remove-subtask-btn:hover:not(:disabled) {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
    transform: scale(1.05);
  }

  .remove-subtask-btn svg {
    width: 16px;
    height: 16px;
    stroke-width: 2;
  }

  .add-subtask-btn {
    padding: 0.75rem 1.25rem;
    border: 2px dashed #d1d5db;
    background: transparent;
    color: #6b7280;
    border-radius: 12px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .add-subtask-btn:hover:not(:disabled) {
    border-color: #4299e1;
    color: #4299e1;
    background: rgba(66, 153, 225, 0.05);
    transform: translateY(-1px);
  }



</style>
