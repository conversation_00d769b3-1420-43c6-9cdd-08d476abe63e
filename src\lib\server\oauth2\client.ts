/**
 * OAuth2 Client Service for Office Flow Integration
 */

import { getOAuth2Config } from './config.js';
import { generatePKCEPair } from './pkce.js';
import { generateState, storeState, consumeState, getActiveStatesCount, type OAuth2State } from './state.js';

/**
 * Office Flow user information
 */
export interface OfficeFlowUser {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  department?: string;
  position?: string;
}

/**
 * OAuth2 token response
 */
export interface OAuth2TokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
  scope: string;
}

/**
 * Generate OAuth2 authorization URL
 */
export function generateAuthorizationUrl(options: {
  redirectTo?: string;
  isLinking?: boolean;
  userId?: string;
  usePopup?: boolean;
} = {}): { url: string; state: string } {
  console.log('[OAuth2 Client] Generating authorization URL with options:', {
    redirectTo: options.redirectTo,
    isLinking: options.isLinking,
    userId: options.userId,
    usePopup: options.usePopup
  });
  
  const config = getOAuth2Config();
  const { codeVerifier, codeChallenge } = generatePKCEPair();
  const state = generateState();

  // Store state data
  const stateData: OAuth2State = {
    state,
    codeVerifier,
    redirectTo: options.redirectTo,
    isLinking: options.isLinking,
    userId: options.userId,
    usePopup: options.usePopup,
    timestamp: Date.now()
  };

  console.log(`[OAuth2 Client] Generated state data:`, {
    state: state.substring(0, 8) + '...',
    codeVerifier: codeVerifier.substring(0, 8) + '...',
    ...options,
    timestamp: new Date(stateData.timestamp).toISOString()
  });

  storeState(stateData);
  console.log(`[OAuth2 Client] Active states after storage: ${getActiveStatesCount()}`);

  // Build authorization URL
  const redirectUri = options.usePopup ? config.POPUP_REDIRECT_URI : config.REDIRECT_URI;
  const params = new URLSearchParams({
    response_type: 'code',
    client_id: config.CLIENT_ID,
    redirect_uri: redirectUri,
    scope: config.SCOPES.join(' '),
    state,
    code_challenge: codeChallenge,
    code_challenge_method: config.CODE_CHALLENGE_METHOD
  });

  const url = `${config.AUTHORIZE_URL}?${params.toString()}`;
  console.log(`[OAuth2 Client] Generated authorization URL (length: ${url.length})`);
  console.log(`[OAuth2 Client] Redirect URI: ${redirectUri}`);

  return { url, state };
}

/**
 * Exchange authorization code for access token
 */
export async function exchangeCodeForToken(
  code: string,
  state: string
): Promise<{ token: OAuth2TokenResponse; stateData: OAuth2State }> {
  console.log(`[OAuth2 Client] Starting token exchange for state: ${state.substring(0, 8)}...`);
  console.log(`[OAuth2 Client] Active states before consume: ${getActiveStatesCount()}`);
  
  const config = getOAuth2Config();

  // Retrieve and validate state
  const stateData = consumeState(state);
  if (!stateData) {
    console.error(`[OAuth2 Client] State validation failed for: ${state.substring(0, 8)}...`);
    throw new Error('Invalid or expired state parameter');
  }

  console.log(`[OAuth2 Client] State validated successfully:`, {
    isLinking: stateData.isLinking,
    userId: stateData.userId,
    usePopup: stateData.usePopup,
    age: Math.round((Date.now() - stateData.timestamp) / 1000) + 's'
  });

  // Exchange code for token
  const redirectUri = stateData.usePopup ? config.POPUP_REDIRECT_URI : config.REDIRECT_URI;
  
  console.log(`[OAuth2 Client] Attempting token exchange with:`, {
    tokenUrl: config.TOKEN_URL,
    redirectUri,
    clientId: config.CLIENT_ID,
    codeLength: code.length
  });
  
  const tokenResponse = await fetch(config.TOKEN_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${Buffer.from(`${config.CLIENT_ID}:${config.CLIENT_SECRET}`).toString('base64')}`
    },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      code,
      redirect_uri: redirectUri,
      code_verifier: stateData.codeVerifier
    })
  });

  if (!tokenResponse.ok) {
    const error = await tokenResponse.text();
    console.error(`[OAuth2 Client] Token exchange failed (${tokenResponse.status}):`, error);

    // Debug: Test client authentication
    console.log('[OAuth2 Client] Testing client authentication...');
    try {
      const debugResponse = await fetch('http://localhost:8092/api/v1/oauth2/client-debug', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${Buffer.from(`${config.CLIENT_ID}:${config.CLIENT_SECRET}`).toString('base64')}`
        }
      });

      if (debugResponse.ok) {
        const debugResult = await debugResponse.text();
        console.log('[OAuth2 Client] Client debug response:', debugResult);
      } else {
        const debugError = await debugResponse.text();
        console.error(`[OAuth2 Client] Client debug failed (${debugResponse.status}):`, debugError);
      }
    } catch (debugErr) {
      console.error('[OAuth2 Client] Client debug request failed:', debugErr);
    }

    throw new Error(`Token exchange failed: ${error}`);
  }

  const token: OAuth2TokenResponse = await tokenResponse.json();
  console.log(`[OAuth2 Client] Token exchange successful:`, {
    tokenType: token.token_type,
    expiresIn: token.expires_in,
    hasRefreshToken: !!token.refresh_token,
    scope: token.scope
  });
  console.log(`[OAuth2 Client] Active states after exchange: ${getActiveStatesCount()}`);

  return { token, stateData };
}

/**
 * Get user information from Office Flow
 */
export async function getUserInfo(accessToken: string): Promise<OfficeFlowUser> {
  console.log('[OAuth2 Client] Fetching user info from Office Flow');
  
  const config = getOAuth2Config();

  const response = await fetch(config.USERINFO_URL, {
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });

  if (!response.ok) {
    const error = await response.text();
    console.error(`[OAuth2 Client] User info request failed (${response.status}):`, error);
    throw new Error(`Failed to get user info: ${error}`);
  }

  const userInfo = await response.json();
  console.log('[OAuth2 Client] User info retrieved successfully:', {
    id: userInfo.sub || userInfo.id,
    email: userInfo.email,
    name: userInfo.name || userInfo.preferred_username,
    hasDepartment: !!userInfo.department,
    hasPosition: !!userInfo.position,
    hasAvatar: !!userInfo.picture
  });

  return {
    id: userInfo.sub || userInfo.id,
    email: userInfo.email,
    name: userInfo.name || userInfo.preferred_username,
    avatar: userInfo.picture,
    department: userInfo.department,
    position: userInfo.position
  };
}

/**
 * Refresh access token
 */
export async function refreshAccessToken(refreshToken: string): Promise<OAuth2TokenResponse> {
  console.log('[OAuth2 Client] Refreshing access token');
  
  const config = getOAuth2Config();

  const response = await fetch(config.TOKEN_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${Buffer.from(`${config.CLIENT_ID}:${config.CLIENT_SECRET}`).toString('base64')}`
    },
    body: new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: refreshToken
    })
  });

  if (!response.ok) {
    const error = await response.text();
    console.error(`[OAuth2 Client] Token refresh failed (${response.status}):`, error);
    throw new Error(`Token refresh failed: ${error}`);
  }

  const newToken = await response.json();
  console.log('[OAuth2 Client] Token refresh successful:', {
    tokenType: newToken.token_type,
    expiresIn: newToken.expires_in,
    hasNewRefreshToken: !!newToken.refresh_token
  });

  return newToken;
}
