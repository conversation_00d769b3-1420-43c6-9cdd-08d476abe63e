/**
 * OAuth2 State Parameter Management
 * Handles generation, storage, and verification of state parameters for CSRF protection
 */

import { randomBytes } from 'crypto';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { OAUTH2_CONFIG } from './config.js';

/**
 * Generate a cryptographically secure state parameter
 */
export function generateState(): string {
  return randomBytes(OAUTH2_CONFIG.STATE_LENGTH).toString('hex');
}

/**
 * OAuth2 state data structure
 */
export interface OAuth2State {
  state: string;
  codeVerifier: string;
  redirectTo?: string;
  isLinking?: boolean;
  userId?: string;
  usePopup?: boolean;
  timestamp: number;
  // Office Flow user info for pending registration
  pendingOfficeFlowUser?: {
    id: string;
    email: string;
    name: string;
    avatar?: string;
    department?: string;
    position?: string;
  };
  // OAuth2 tokens for pending registration
  pendingTokens?: {
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
  };
}

/**
 * File-based state storage configuration
 */
const STATE_FILE_PATH = join(process.cwd(), '.oauth2-states', 'states.json');
const STATE_EXPIRY_TIME = 20 * 60 * 1000; // 20 minutes

/**
 * In-memory cache for state storage
 */
let stateCache: Map<string, OAuth2State> | null = null;

/**
 * Ensure state storage directory exists
 */
function ensureStateDirectory(): void {
  const dir = dirname(STATE_FILE_PATH);
  if (!existsSync(dir)) {
    try {
      mkdirSync(dir, { recursive: true });
      console.log('[OAuth2 State] Created state storage directory:', dir);
    } catch (error) {
      console.error('[OAuth2 State] Failed to create state storage directory:', error);
      throw new Error('Failed to initialize OAuth2 state storage');
    }
  }
}

/**
 * Load state storage from file
 */
function loadStateStorage(): Map<string, OAuth2State> {
  try {
    ensureStateDirectory();

    if (!existsSync(STATE_FILE_PATH)) {
      console.log('[OAuth2 State] State file does not exist, creating new storage');
      return new Map();
    }

    const data = readFileSync(STATE_FILE_PATH, 'utf-8');
    const parsed = JSON.parse(data);
    const stateMap = new Map<string, OAuth2State>();

    // Convert array back to Map and clean expired states
    const now = Date.now();
    let expiredCount = 0;

    for (const [key, value] of parsed) {
      // Check if state is still valid (not expired)
      if (value.timestamp + STATE_EXPIRY_TIME > now) {
        stateMap.set(key, value);
      } else {
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      console.log(`[OAuth2 State] Cleaned ${expiredCount} expired states on load`);
      saveStateStorage(stateMap);
    }

    console.log(`[OAuth2 State] Loaded ${stateMap.size} active states from file`);
    return stateMap;
  } catch (error) {
    console.error('[OAuth2 State] Failed to load state storage, creating new:', error);
    return new Map();
  }
}

/**
 * Save state storage to file and update cache
 */
function saveStateStorage(stateMap: Map<string, OAuth2State>): void {
  try {
    ensureStateDirectory();

    // Convert Map to array for JSON serialization
    const data = JSON.stringify(Array.from(stateMap.entries()), null, 2);
    writeFileSync(STATE_FILE_PATH, data, 'utf-8');

    // Update cache
    stateCache = stateMap;

    console.log(`[OAuth2 State] Saved ${stateMap.size} states to file`);
  } catch (error) {
    console.error('[OAuth2 State] Failed to save state storage:', error);
    throw new Error('Failed to persist OAuth2 state');
  }
}

/**
 * Get current state storage (with caching)
 */
function getStateStorage(): Map<string, OAuth2State> {
  if (!stateCache) {
    stateCache = loadStateStorage();
  }
  return stateCache;
}

/**
 * Store OAuth2 state
 */
export function storeState(stateData: OAuth2State): void {
  try {
    console.log(`[OAuth2 State] Storing state: ${stateData.state} (isLinking: ${stateData.isLinking}, userId: ${stateData.userId})`);

    const stateStorage = getStateStorage();
    stateStorage.set(stateData.state, stateData);

    // Clean up expired states (older than 20 minutes)
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, value] of stateStorage.entries()) {
      if (value.timestamp + STATE_EXPIRY_TIME <= now) {
        stateStorage.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`[OAuth2 State] Cleaned ${cleanedCount} expired states during store`);
    }

    saveStateStorage(stateStorage);
    console.log(`[OAuth2 State] Successfully stored state, total active states: ${stateStorage.size}`);
  } catch (error) {
    console.error('[OAuth2 State] Failed to store state:', error);
    throw new Error('Failed to store OAuth2 state');
  }
}

/**
 * Retrieve and remove OAuth2 state
 */
export function consumeState(state: string): OAuth2State | null {
  try {
    console.log(`[OAuth2 State] Attempting to consume state: ${state}`);

    const stateStorage = getStateStorage();
    const stateData = stateStorage.get(state);

    if (!stateData) {
      console.warn(`[OAuth2 State] State not found: ${state}`);
      return null;
    }

    // Check if state is not expired (20 minutes)
    const expiredTime = Date.now() - STATE_EXPIRY_TIME;
    if (stateData.timestamp < expiredTime) {
      console.warn(`[OAuth2 State] State expired: ${state} (age: ${Math.round((Date.now() - stateData.timestamp) / 1000 / 60)} minutes)`);
      stateStorage.delete(state);
      saveStateStorage(stateStorage);
      return null;
    }

    // Remove consumed state
    stateStorage.delete(state);
    saveStateStorage(stateStorage);

    console.log(`[OAuth2 State] Successfully consumed state: ${state} (isLinking: ${stateData.isLinking}, userId: ${stateData.userId})`);
    console.log(`[OAuth2 State] Remaining active states: ${stateStorage.size}`);

    return stateData;
  } catch (error) {
    console.error('[OAuth2 State] Failed to consume state:', error);
    return null;
  }
}

/**
 * Verify state parameter
 */
export function verifyState(state: string): boolean {
  try {
    console.log(`[OAuth2 State] Verifying state: ${state}`);

    const stateStorage = getStateStorage();
    const exists = stateStorage.has(state);

    if (exists) {
      const stateData = stateStorage.get(state);
      const now = Date.now();
      const isValid = stateData && (stateData.timestamp + STATE_EXPIRY_TIME > now);

      if (!isValid && stateData) {
        console.warn(`[OAuth2 State] State exists but expired: ${state} (age: ${Math.round((Date.now() - stateData.timestamp) / 1000 / 60)} minutes)`);
        // Clean up expired state
        stateStorage.delete(state);
        saveStateStorage(stateStorage);
        return false;
      }

      console.log(`[OAuth2 State] State verification result: ${isValid}`);
      return isValid;
    }

    console.log(`[OAuth2 State] State not found: ${state}`);
    return false;
  } catch (error) {
    console.error('[OAuth2 State] Failed to verify state:', error);
    return false;
  }
}

/**
 * Get active states count (for debugging)
 */
export function getActiveStatesCount(): number {
  try {
    const stateStorage = getStateStorage();
    return stateStorage.size;
  } catch (error) {
    console.error('[OAuth2 State] Failed to get active states count:', error);
    return 0;
  }
}

/**
 * Clear all expired states (manual cleanup)
 */
export function clearExpiredStates(): number {
  try {
    const stateStorage = getStateStorage();
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, value] of stateStorage.entries()) {
      if (value.timestamp + STATE_EXPIRY_TIME <= now) {
        stateStorage.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      saveStateStorage(stateStorage);
      console.log(`[OAuth2 State] Manual cleanup removed ${cleanedCount} expired states`);
    }

    return cleanedCount;
  } catch (error) {
    console.error('[OAuth2 State] Failed to clear expired states:', error);
    return 0;
  }
}

/**
 * Store pending Office Flow user info for registration
 */
export function storePendingOfficeFlowUser(
  officeFlowUser: {
    id: string;
    email: string;
    name: string;
    avatar?: string;
    department?: string;
    position?: string;
  },
  tokens: {
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
  }
): string {
  try {
    const pendingState = generateState();
    const stateData: OAuth2State = {
      state: pendingState,
      codeVerifier: '', // Not needed for pending registration
      timestamp: Date.now(),
      pendingOfficeFlowUser: officeFlowUser,
      pendingTokens: tokens
    };

    console.log(`[OAuth2 State] Storing pending Office Flow user: ${officeFlowUser.email} with state: ${pendingState}`);
    storeState(stateData);

    return pendingState;
  } catch (error) {
    console.error('[OAuth2 State] Failed to store pending Office Flow user:', error);
    throw new Error('Failed to store pending Office Flow user');
  }
}

/**
 * Retrieve and consume pending Office Flow user info
 */
export function consumePendingOfficeFlowUser(pendingState: string): {
  officeFlowUser: {
    id: string;
    email: string;
    name: string;
    avatar?: string;
    department?: string;
    position?: string;
  };
  tokens: {
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
  };
} | null {
  try {
    console.log(`[OAuth2 State] Attempting to consume pending Office Flow user with state: ${pendingState}`);

    const stateData = consumeState(pendingState);
    if (!stateData || !stateData.pendingOfficeFlowUser || !stateData.pendingTokens) {
      console.warn(`[OAuth2 State] No pending Office Flow user found for state: ${pendingState}`);
      return null;
    }

    console.log(`[OAuth2 State] Successfully retrieved pending Office Flow user: ${stateData.pendingOfficeFlowUser.email}`);

    return {
      officeFlowUser: stateData.pendingOfficeFlowUser,
      tokens: stateData.pendingTokens
    };
  } catch (error) {
    console.error('[OAuth2 State] Failed to consume pending Office Flow user:', error);
    return null;
  }
}
