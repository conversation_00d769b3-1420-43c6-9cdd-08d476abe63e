import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { validateEmail, verifyPassword, generateJWT } from '$lib/server/auth.js';
import { getUserByEmail, createSession } from '$lib/server/db/operations.js';

export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    const { email, password } = await request.json();

    // Validate input
    if (!email || !password || !validateEmail(email)) {
      return json({ error: 'Invalid email or password' }, { status: 400 });
    }

    // Get user
    const user = await getUserByEmail(email);
    if (!user) {
      return json({ error: 'Invalid email or password' }, { status: 400 });
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, user.password);
    if (!isValidPassword) {
      return json({ error: 'Invalid email or password' }, { status: 400 });
    }

    // Check if user is verified
    if (!user.isVerified) {
      return json({ error: 'Please verify your email first' }, { status: 400 });
    }

    // Generate JWT and create session
    const jwtPayload = { userId: user.id, email: user.email };
    const token = generateJWT(jwtPayload);
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

    await createSession({
      userId: user.id,
      token,
      expiresAt
    });

    // Set cookie
    cookies.set('auth-token', token, {
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 // 7 days
    });

    return json({ 
      message: 'Login successful',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        isVerified: user.isVerified
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
